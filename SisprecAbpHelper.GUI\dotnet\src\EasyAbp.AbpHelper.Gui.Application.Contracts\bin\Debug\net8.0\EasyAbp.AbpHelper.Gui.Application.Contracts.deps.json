{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"EasyAbp.AbpHelper.Gui.Application.Contracts/2.16.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Account.Application.Contracts": "9.1.1", "Volo.Abp.Identity.Application.Contracts": "9.1.1", "Volo.Abp.ObjectExtending": "9.1.1"}, "runtime": {"EasyAbp.AbpHelper.Gui.Application.Contracts.dll": {}}}, "AsyncKeyedLock/7.1.3": {"runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"assemblyVersion": "7.1.3.0", "fileVersion": "7.1.3.0"}}}, "JetBrains.Annotations/2024.2.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2024.2.0.0"}}}, "Microsoft.AspNetCore.Authorization/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Metadata/9.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Composite/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Localization/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Localization.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.Localization.Abstractions/9.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "9.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/9.0.0": {"runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/9.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/*******": {"runtime": {"lib/net8.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/9.0.0": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Text.Json/9.0.0": {"dependencies": {"System.IO.Pipelines": "9.0.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "TimeZoneConverter/6.1.0": {"runtime": {"lib/net6.0/TimeZoneConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Account.Application.Contracts/9.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Identity.Application.Contracts": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Account.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Auditing.Contracts/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization/9.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.1.1", "Volo.Abp.Localization": "9.1.1", "Volo.Abp.MultiTenancy": "9.1.1", "Volo.Abp.Security": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization.Abstractions/9.1.1": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.0", "Volo.Abp.MultiTenancy.Abstractions": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BackgroundWorkers/9.1.1": {"dependencies": {"Volo.Abp.Threading": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/9.1.1": {"dependencies": {"JetBrains.Annotations": "2024.2.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Localization": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "9.0.0", "System.Linq.Dynamic.Core": "*******", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Data/9.1.1": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "9.1.1", "Volo.Abp.ObjectExtending": "9.1.1", "Volo.Abp.Uow": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application.Contracts/9.1.1": {"dependencies": {"Volo.Abp.Auditing.Contracts": "9.1.1", "Volo.Abp.Data": "9.1.1", "Volo.Abp.Localization": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.DistributedLocking.Abstractions/9.1.1": {"dependencies": {"AsyncKeyedLock": "7.1.3", "Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus/9.1.1": {"dependencies": {"Volo.Abp.BackgroundWorkers": "9.1.1", "Volo.Abp.DistributedLocking.Abstractions": "9.1.1", "Volo.Abp.EventBus.Abstractions": "9.1.1", "Volo.Abp.Guids": "9.1.1", "Volo.Abp.Json": "9.1.1", "Volo.Abp.MultiTenancy": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus.Abstractions/9.1.1": {"dependencies": {"Volo.Abp.ObjectExtending": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Features/9.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.1.1", "Volo.Abp.Localization": "9.1.1", "Volo.Abp.MultiTenancy": "9.1.1", "Volo.Abp.Validation": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Guids/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Application.Contracts/9.1.1": {"dependencies": {"Volo.Abp.Authorization": "9.1.1", "Volo.Abp.Identity.Domain.Shared": "9.1.1", "Volo.Abp.PermissionManagement.Application.Contracts": "9.1.1", "Volo.Abp.Users.Abstractions": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Domain.Shared/9.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Auditing.Contracts": "9.1.1", "Volo.Abp.Features": "9.1.1", "Volo.Abp.Users.Domain.Shared": "9.1.1", "Volo.Abp.Validation": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json/9.1.1": {"dependencies": {"Volo.Abp.Json.SystemTextJson": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.Abstractions/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.SystemTextJson/9.1.1": {"dependencies": {"System.Text.Json": "9.0.0", "Volo.Abp.Data": "9.1.1", "Volo.Abp.Json.Abstractions": "9.1.1", "Volo.Abp.Timing": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization/9.1.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "9.1.1", "Volo.Abp.Settings": "9.1.1", "Volo.Abp.Threading": "9.1.1", "Volo.Abp.VirtualFileSystem": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization.Abstractions/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy/9.1.1": {"dependencies": {"Volo.Abp.Data": "9.1.1", "Volo.Abp.EventBus.Abstractions": "9.1.1", "Volo.Abp.MultiTenancy.Abstractions": "9.1.1", "Volo.Abp.Security": "9.1.1", "Volo.Abp.Settings": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy.Abstractions/9.1.1": {"dependencies": {"Volo.Abp.Localization": "9.1.1", "Volo.Abp.VirtualFileSystem": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectExtending/9.1.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "9.1.1", "Volo.Abp.Validation.Abstractions": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Application.Contracts/9.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.1.1", "Volo.Abp.Ddd.Application.Contracts": "9.1.1", "Volo.Abp.PermissionManagement.Domain.Shared": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Domain.Shared/9.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Validation": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Security/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Settings/9.1.1": {"dependencies": {"Volo.Abp.Data": "9.1.1", "Volo.Abp.Localization.Abstractions": "9.1.1", "Volo.Abp.Security": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Threading/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Timing/9.1.1": {"dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "9.1.1", "Volo.Abp.Settings": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Uow/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Abstractions/9.1.1": {"dependencies": {"Volo.Abp.EventBus": "9.1.1", "Volo.Abp.MultiTenancy": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Domain.Shared/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation/9.1.1": {"dependencies": {"Volo.Abp.Localization": "9.1.1", "Volo.Abp.Validation.Abstractions": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation.Abstractions/9.1.1": {"dependencies": {"Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.VirtualFileSystem/9.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "9.0.0", "Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Volo.Abp.Core": "9.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"EasyAbp.AbpHelper.Gui.Application.Contracts/2.16.0": {"type": "project", "serviceable": false, "sha512": ""}, "AsyncKeyedLock/7.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-9sxnBt4i7CwuRBARyafoozJwIE3RFS/xcjhjfLeMZhVPzvaRI9fDqX+EOHMmkK6I4/+CWYhKflcTRWozG1Myzw==", "path": "asynckeyedlock/7.1.3", "hashPath": "asynckeyedlock.7.1.3.nupkg.sha512"}, "JetBrains.Annotations/2024.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GNnqCFW/163p1fOehKx0CnAqjmpPrUSqrgfHM6qca+P+RN39C9rhlfZHQpJhxmQG/dkOYe/b3Z0P8b6Kv5m1qw==", "path": "jetbrains.annotations/2024.2.0", "hashPath": "jetbrains.annotations.2024.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qDJlBC5pUQ/3o6/C6Vuo9CGKtV5TAe5AdKeHvDR2bgmw8vwPxsAy3KG5eU0i1C+iAUNbmq+iDTbiKt16f9pRiA==", "path": "microsoft.aspnetcore.authorization/9.0.0", "hashPath": "microsoft.aspnetcore.authorization.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-X81C891nMuWgzNHyZ0C3s+blSDxRHzQHDFYQoOKtFvFuxGq3BbkLbc5CfiCqIzA/sWIfz6u8sGBgwntQwBJWBw==", "path": "microsoft.aspnetcore.metadata/9.0.0", "hashPath": "microsoft.aspnetcore.metadata.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-owmu2Cr3IQ8yQiBleBHlGk8dSQ12oaF2e7TpzwJKEl4m84kkZJjEY1n33L67Y3zM5jPOjmmbdHjbfiL0RqcMRQ==", "path": "microsoft.bcl.asyncinterfaces/9.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qD+hdkBtR9Ps7AxfhTJCnoVakkadHgHlD1WRN0QHGHod+SDuca1ao1kF4G2rmpAz2AEKrE2N2vE8CCCZ+ILnNw==", "path": "microsoft.extensions.configuration.commandline/9.0.0", "hashPath": "microsoft.extensions.configuration.commandline.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v5R638eNMxksfXb7MFnkPwLPp+Ym4W/SIGNuoe8qFVVyvygQD5DdLusybmYSJEr9zc1UzWzim/ATKeIOVvOFDg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "path": "microsoft.extensions.configuration.json/9.0.0", "hashPath": "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FShWw8OysquwV7wQHYkkz0VWsJSo6ETUu4h7tJRMtnG0uR+tzKOldhcO8xB1pGSOI3Ng6v3N1Q94YO8Rzq1P6A==", "path": "microsoft.extensions.configuration.usersecrets/9.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-*******************************************/HlDAlnrhR9dvlURfFz428A+RTCJpUyB+aKTA6AgVcQ==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IjNhwxaJ1nvl6K49AhaNLTXI0RgmGboWwqBv2NttC8RXSOjgucP8qYapCXrJS/Xf9hSHILJ7NJNdY9F6QjPqQA==", "path": "microsoft.extensions.fileproviders.composite/9.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Ev1goLIvggLF6uCs6oZvdr9JM+2b1Zj+4FLdBWNW5iw3tm2BymVIb0yMsjnQTBWL7YUmqVWH3u45hSqOfvuqg==", "path": "microsoft.extensions.fileproviders.embedded/9.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yUKJgu81ExjvqbNWqZKshBbLntZMbMVz/P7Way2SBx7bMqA08Mfdc9O7hWDKAiSp+zPUGT6LKcSCQIPeDK+CCw==", "path": "microsoft.extensions.hosting.abstractions/9.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Up8Juy8Bh+vL+fXmMWsoSg/G6rszmLFiF44aI2tpOMJE7Ln4D9s37YxOOm81am4Z+V7g8Am3AgVwHYJzi+cL/g==", "path": "microsoft.extensions.localization/9.0.0", "hashPath": "microsoft.extensions.localization.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wc7PaRhPOnio5Csj80b3UgBWA5l6bp28EhGem7gtfpVopcwbkfPb2Sk8Cu6eBnIW3ZNf1YUgYJzwtjzZEM8+iw==", "path": "microsoft.extensions.localization.abstractions/9.0.0", "hashPath": "microsoft.extensions.localization.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob3FXsXkcSMQmGZi7qP07EQ39kZpSBlTcAZLbJLdI4FIf0Jug8biv2HTavWmnTirchctPlq9bl/26CXtQRguzA==", "path": "microsoft.extensions.options.configurationextensions/9.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QhkXUl2gNrQtvPmtBTQHb0YsUrDiDQ2QS09YbtTTiSjGcf7NBqtYbrG/BE06zcBPCKEwQGzIv13IVdXNOSub2w==", "path": "system.collections.immutable/9.0.0", "hashPath": "system.collections.immutable.9.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eA3cinogwaNB4jdjQHOP3Z3EuyiDII7MT35jgtnsA4vkn0LUrrSHsU0nzHTzFzmaFYeKV7MYyMxOocFzsBHpTw==", "path": "system.io.pipelines/9.0.0", "hashPath": "system.io.pipelines.9.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/*******": {"type": "package", "serviceable": true, "sha512": "sha512-u1ome4HL3OS5lq2QbxIAmHv+i5uewSf2hqosGr8O5FjkseqSx4Fmt1Pn+8fjyex7/rQOOYrXk+iTkxxEgSc9yA==", "path": "system.linq.dynamic.core/*******", "hashPath": "system.linq.dynamic.core.*******.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "TimeZoneConverter/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "path": "timezoneconverter/6.1.0", "hashPath": "timezoneconverter.6.1.0.nupkg.sha512"}, "Volo.Abp.Account.Application.Contracts/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZW/Qr85aNrsnWTG9srLTj+r0zXY/Y3MwUsoSyG4bgnoiNk8aCEQnu8+H8AoLmaTPcrrAf3Ytt/QF258IzoLveg==", "path": "volo.abp.account.application.contracts/9.1.1", "hashPath": "volo.abp.account.application.contracts.9.1.1.nupkg.sha512"}, "Volo.Abp.Auditing.Contracts/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ztn2uDPDB5hagv8DPPtp/ZcwhoKG3MxHFRS1RX5Q8ncQWyYyxOWFPQ2MySpqMRtIWAumoO3ZL7AzBZEjOsu9Hg==", "path": "volo.abp.auditing.contracts/9.1.1", "hashPath": "volo.abp.auditing.contracts.9.1.1.nupkg.sha512"}, "Volo.Abp.Authorization/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HiAu2Aj/eMW0uoTzf7wYnsaRkmWTqLRLtxtobe1db6U62SinBNtsMmiAGij5/uoq0EsBPKn8aQqaCcULfR3ciQ==", "path": "volo.abp.authorization/9.1.1", "hashPath": "volo.abp.authorization.9.1.1.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Svz0LuwqGKq3Y0wod9qA1As6rray0CD6Jh3QFz39Im/Ha8uppF2iz5B0k/P7J1Al+U8meC4bRwd2EnIgLEzMTg==", "path": "volo.abp.authorization.abstractions/9.1.1", "hashPath": "volo.abp.authorization.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.BackgroundWorkers/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON>bSLY0EMceTVBRvtG5R9cLOqS6QCZuhjtBO6PRH1WBsAbe7EcCkeyTOJl85Cg10Cdn3hDSgBC9EldVApcSyw==", "path": "volo.abp.backgroundworkers/9.1.1", "hashPath": "volo.abp.backgroundworkers.9.1.1.nupkg.sha512"}, "Volo.Abp.Core/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LIIQF8dp4oNuTNHnyft/BUl5gu1311lBN4NmzsMqkyvbgC5lK4WTvDRFvoZ0pLCpYfxcvqKKiNgzmSNjLUlrig==", "path": "volo.abp.core/9.1.1", "hashPath": "volo.abp.core.9.1.1.nupkg.sha512"}, "Volo.Abp.Data/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-RpRSoPXywVmdSON/bkRRaCYzJR5d4f4qSf2GWRQ8ES7h9MFjihhpiyXOfN5hucB+ShwLdRXvDWrRrVK0Y7ztmA==", "path": "volo.abp.data/9.1.1", "hashPath": "volo.abp.data.9.1.1.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-c1xWzk1ECIupJtHuSN1WGgf7xvvu+DcvBPJmEKEdSXAP5aGaT+KYmQXK4HDurhnTiSLpgO+pLvMvk4qwGBaVbA==", "path": "volo.abp.ddd.application.contracts/9.1.1", "hashPath": "volo.abp.ddd.application.contracts.9.1.1.nupkg.sha512"}, "Volo.Abp.DistributedLocking.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-swWgOgEvcKZ1MIU0yJx0kCYnn+FsXMtYeyygTgAiI6YTIKsMJtpjzwDr6/rlPV/gAWfvKS++dT+BF4D38XE37w==", "path": "volo.abp.distributedlocking.abstractions/9.1.1", "hashPath": "volo.abp.distributedlocking.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.EventBus/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sWWHdzBhgeRkZ0gwxZJF9d89YPB9xOoc5cxsahdZaXiLIT3EV+GgI94dqmCxk2oYsBNFtvVnl0HIAAuAAe/zBQ==", "path": "volo.abp.eventbus/9.1.1", "hashPath": "volo.abp.eventbus.9.1.1.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-XeKz4SAgopPHKoLSItwxpuA0vKkzgKgPgBAASLzy2Nk6LHto3/DikGQE5Gw+1B6UHHrFI4VaJIasLX1SvFradg==", "path": "volo.abp.eventbus.abstractions/9.1.1", "hashPath": "volo.abp.eventbus.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.Features/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-cwGsXoDGJDsEUhRngFVpn1Rp9TAgHhH/UI9FmkhHpJlsRa00uqlONWzv6wevda4k6SEgyf7P+BkPPhPzXdJW0w==", "path": "volo.abp.features/9.1.1", "hashPath": "volo.abp.features.9.1.1.nupkg.sha512"}, "Volo.Abp.Guids/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-9MhrH+n43GWE3CH+BKz2q9LeRuJKYGdWS76TE58VQ9TRIoAxMqttD85jnPJfNqen5vkoygeeU6d0r9NxbYE6gg==", "path": "volo.abp.guids/9.1.1", "hashPath": "volo.abp.guids.9.1.1.nupkg.sha512"}, "Volo.Abp.Identity.Application.Contracts/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-a6MEqBmUvN62FiDb35qtoAP9zExqPdVgqoarlo7tf3WigQQn20FkKizLH79c2POswYy6xqO8Lb9/kbcZweKDQA==", "path": "volo.abp.identity.application.contracts/9.1.1", "hashPath": "volo.abp.identity.application.contracts.9.1.1.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jp1F3fvHHROnlUkqYdFAVp8P1ywS69IAlD1B6AzpP/NQyMP9yodFuqNUV236IHFF3VLo6zMTcBYrAxod47TOiw==", "path": "volo.abp.identity.domain.shared/9.1.1", "hashPath": "volo.abp.identity.domain.shared.9.1.1.nupkg.sha512"}, "Volo.Abp.Json/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jpsiu2C6X/a0gENhuCtXKNmgSIYz0t06qI2p4KyLmxeW5IVUwxgPNbNdEenZVL8HmfOnXZHYvQ2QoWyus/XNPg==", "path": "volo.abp.json/9.1.1", "hashPath": "volo.abp.json.9.1.1.nupkg.sha512"}, "Volo.Abp.Json.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-V/wkhNjVLhvqI6B9wznT8IfGLzR8MzVZvE4pwIgTUd1DU4619rBVDxBBqwh3UYcmZFRTzs1wqRssuhNpjvnDNQ==", "path": "volo.abp.json.abstractions/9.1.1", "hashPath": "volo.abp.json.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.Json.SystemTextJson/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-EqTRrf5L7yO/HE5/ITFckaSeOjQkUTbwOQNLzmSe5uK1DAQFi+RWe59oEx8THvbDArzdOrboqIR5cFCpkR01wg==", "path": "volo.abp.json.systemtextjson/9.1.1", "hashPath": "volo.abp.json.systemtextjson.9.1.1.nupkg.sha512"}, "Volo.Abp.Localization/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ovk3u0WEXXwTlToOOubIGmJ8Xk2qMJ7sJB01V8I3YIIp5jklZLkM3nLNz/GSI3co7gKYcQZbGSATZleNxgggmQ==", "path": "volo.abp.localization/9.1.1", "hashPath": "volo.abp.localization.9.1.1.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+b299wns4UW/pVjGFHbRIlkgFZSnpHdbghPDQkuYuJ27PuDO/tv9v3mhBr4HIHgpf8thI1sZkUNnhUv9juEmRw==", "path": "volo.abp.localization.abstractions/9.1.1", "hashPath": "volo.abp.localization.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-r7vTWL2TlgREvChrwW4E71gsDjU1Bp+eQfFE88StBcrtnlsv7SulBk0fjzt6Jf2HeOslXGr87PgSkiwOioQ4FA==", "path": "volo.abp.multitenancy/9.1.1", "hashPath": "volo.abp.multitenancy.9.1.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-SISnA3Ec3dUWvFOXiraNTWvnLLwd/rZtRhPTRHnBDf/2Zd3JVS8XCZ00j2ZpdZzDqTZJ5dBbr0vYS4KCJ9m4FQ==", "path": "volo.abp.multitenancy.abstractions/9.1.1", "hashPath": "volo.abp.multitenancy.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.ObjectExtending/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+VEjDl4E0GashdSYzw6r6XMX4ouqaQPL0yIuvWKHDtensc9N9d2TdfmDP59iHCYZP9fPpdscWXlw1oH2jnrv6A==", "path": "volo.abp.objectextending/9.1.1", "hashPath": "volo.abp.objectextending.9.1.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Application.Contracts/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-FEX33Cls+kd+KxQTV1D3IbCO/1S2sdqPr5QBSsK7oWExbJUDazdi7BapP2J9/0cAnWm0No8quTXh5E8S7LcoVQ==", "path": "volo.abp.permissionmanagement.application.contracts/9.1.1", "hashPath": "volo.abp.permissionmanagement.application.contracts.9.1.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Shared/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-DevQASxm0OI62lr6xP3LuaHKfi/TK92EOGRP3VsIPGcJ3RbuRcDWKFYzeLNH62gDmFBmS83PQGowh7uopZarrQ==", "path": "volo.abp.permissionmanagement.domain.shared/9.1.1", "hashPath": "volo.abp.permissionmanagement.domain.shared.9.1.1.nupkg.sha512"}, "Volo.Abp.Security/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-66ALiXW7m+pfCm2YNNwZgbIMtPdmvgAXP3JfH6TAG3iTmi4ZK258HnNgAwsz/P0+wRPWHgh79r8bAH9kHwQhug==", "path": "volo.abp.security/9.1.1", "hashPath": "volo.abp.security.9.1.1.nupkg.sha512"}, "Volo.Abp.Settings/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MSPYA5WYErBfrI6EOmhQ5tcYp66OKtMX2iTlj3wTkeSNUyQIDxQY8JfzQEPmSHz9PeawvfznGTqrKHQyx9jgYQ==", "path": "volo.abp.settings/9.1.1", "hashPath": "volo.abp.settings.9.1.1.nupkg.sha512"}, "Volo.Abp.Threading/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-V5yonA17VpRjIgpiF9q1z0ESeCAtYtJZnvqZ37DEm51nbtmzhhRFXqvyPi8Hhh42VqYAssbVKUeqvomn+Ny/qw==", "path": "volo.abp.threading/9.1.1", "hashPath": "volo.abp.threading.9.1.1.nupkg.sha512"}, "Volo.Abp.Timing/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-5+UPfMBEufykszl75xAydYq2BrgDhD9NxjL9YmTfVdoooKcNRsULMfncyiamGpDoOBpE+u3KghzR4I7erLMCKA==", "path": "volo.abp.timing/9.1.1", "hashPath": "volo.abp.timing.9.1.1.nupkg.sha512"}, "Volo.Abp.Uow/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wp6DZtIIpNRx1kDbKGIZeZpMpjq0UrHZm/3rTqzNEIumbrdIMlxxa7GuNlLYiwG9xnzotjgG/HRdBIDNZbGIlA==", "path": "volo.abp.uow/9.1.1", "hashPath": "volo.abp.uow.9.1.1.nupkg.sha512"}, "Volo.Abp.Users.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-aLX/tRqnJ/K6qKzW6BHaXbXYoF056BkuFRBZX3MLD01TM6ItidjwaTNPxCpzc1LUUH+VYIugElGQJ4DDShjdhw==", "path": "volo.abp.users.abstractions/9.1.1", "hashPath": "volo.abp.users.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dasPqDHF39g5pl3aj2YevrupNXXwVNrDuODo/I0x40O7GkeCfH1k7Xx77IETrw2BQZ4l4sdAyr7fH4tASpMNWA==", "path": "volo.abp.users.domain.shared/9.1.1", "hashPath": "volo.abp.users.domain.shared.9.1.1.nupkg.sha512"}, "Volo.Abp.Validation/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-3oAX1geZDl13NeBzrqSL5RzDWHPqEssTizLb5SkMi0xGKFTFGPkKqifC9BPCfBHNPFjJ7DVRsukxVYdCbhfB5w==", "path": "volo.abp.validation/9.1.1", "hashPath": "volo.abp.validation.9.1.1.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sfEKRyojDreqXb17VkpplmYuWXGdNLDtCAp//9USq1IEH9k7X1+o4rmhGthF5FaOZePLp53XNydULOIxQmOhMw==", "path": "volo.abp.validation.abstractions/9.1.1", "hashPath": "volo.abp.validation.abstractions.9.1.1.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/9.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-qFt1+o3mrQEZ99qb17GXomcnvpJBgaRpgZ6tR65oP4wVQyAIHoeMy0X3g5ggLKi6eNWwbBtl2t8UxRhbjTZq0Q==", "path": "volo.abp.virtualfilesystem/9.1.1", "hashPath": "volo.abp.virtualfilesystem.9.1.1.nupkg.sha512"}}}