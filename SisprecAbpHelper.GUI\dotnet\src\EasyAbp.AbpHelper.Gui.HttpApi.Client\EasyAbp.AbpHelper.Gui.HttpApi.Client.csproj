<Project Sdk="Microsoft.NET.Sdk">
    <Import Project="..\..\common.props" />
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>AbpTools.AbpHelper.Gui</RootNamespace>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Volo.Abp.Account.HttpApi.Client"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Identity.HttpApi.Client"
                          Version="$(AbpVersion)" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\AbpTools.AbpHelper.Gui.Application.Contracts\AbpTools.AbpHelper.Gui.Application.Contracts.csproj" />
    </ItemGroup>
    <ItemGroup>
        <EmbeddedResource Include="**\*generate-proxy.json" />
        <Content Remove="**\*generate-proxy.json" />
    </ItemGroup>
</Project>