﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Login.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.AbpCli.Login
{
    public interface IAbpCliLoginAppService : IApplicationService
    {
        Task<ServiceExecutionResult> LoginAsync(AbpLoginInput input);
        
        Task<ServiceExecutionResult> LogoutAsync();
    }
}