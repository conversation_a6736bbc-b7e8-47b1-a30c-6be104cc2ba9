﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.29020.237
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{CA9AC87F-097E-4F15-8393-4BC07735A5B0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{04DBDB01-70F4-4E06-B468-8F87850B22BE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AbpTools.AbpHelper.Gui.HttpApi.Client", "src\AbpTools.AbpHelper.Gui.HttpApi.Client\AbpTools.AbpHelper.Gui.HttpApi.Client.csproj", "{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AbpTools.AbpHelper.Gui.TestBase", "test\AbpTools.AbpHelper.Gui.TestBase\AbpTools.AbpHelper.Gui.TestBase.csproj", "{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AbpTools.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp", "test\AbpTools.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp\AbpTools.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp.csproj", "{EF480016-9127-4916-8735-D2466BDBC582}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AbpTools.AbpHelper.Gui.Blazor", "src\AbpTools.AbpHelper.Gui.Blazor\AbpTools.AbpHelper.Gui.Blazor.csproj", "{27B2DDC7-8B75-4322-A312-25419C15D9D8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AbpTools.AbpHelper.Gui.HttpApi", "src\AbpTools.AbpHelper.Gui.HttpApi\AbpTools.AbpHelper.Gui.HttpApi.csproj", "{EA728778-AF6E-41A9-8A4C-A878A8E51DD0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AbpTools.AbpHelper.Gui.Application.Contracts", "src\AbpTools.AbpHelper.Gui.Application.Contracts\AbpTools.AbpHelper.Gui.Application.Contracts.csproj", "{63B6D95A-C16E-475F-A871-1F97EA5A629C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AbpTools.AbpHelper.Gui.Application", "src\AbpTools.AbpHelper.Gui.Application\AbpTools.AbpHelper.Gui.Application.csproj", "{DF9F3996-3664-40E8-B6C0-1792AFD1BAE3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AbpTools.AbpHelper.Gui.Application.Tests", "test\AbpTools.AbpHelper.Gui.Application.Tests\AbpTools.AbpHelper.Gui.Application.Tests.csproj", "{2C603A1A-A8E9-4EC7-AC03-DCDFB8A9E3BF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AbpHelper.Core", "..\AbpHelper.CLI\src\AbpHelper.Core\AbpHelper.Core.csproj", "{8FF0D556-CF73-4796-935B-DBA1AFC643C8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "external", "external", "{B6FCF58A-0ABD-4AA3-A3F4-122A18E239E6}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF480016-9127-4916-8735-D2466BDBC582}.Release|Any CPU.Build.0 = Release|Any CPU
		{27B2DDC7-8B75-4322-A312-25419C15D9D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27B2DDC7-8B75-4322-A312-25419C15D9D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27B2DDC7-8B75-4322-A312-25419C15D9D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27B2DDC7-8B75-4322-A312-25419C15D9D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA728778-AF6E-41A9-8A4C-A878A8E51DD0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA728778-AF6E-41A9-8A4C-A878A8E51DD0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA728778-AF6E-41A9-8A4C-A878A8E51DD0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA728778-AF6E-41A9-8A4C-A878A8E51DD0}.Release|Any CPU.Build.0 = Release|Any CPU
		{63B6D95A-C16E-475F-A871-1F97EA5A629C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63B6D95A-C16E-475F-A871-1F97EA5A629C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63B6D95A-C16E-475F-A871-1F97EA5A629C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63B6D95A-C16E-475F-A871-1F97EA5A629C}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF9F3996-3664-40E8-B6C0-1792AFD1BAE3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF9F3996-3664-40E8-B6C0-1792AFD1BAE3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF9F3996-3664-40E8-B6C0-1792AFD1BAE3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF9F3996-3664-40E8-B6C0-1792AFD1BAE3}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C603A1A-A8E9-4EC7-AC03-DCDFB8A9E3BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C603A1A-A8E9-4EC7-AC03-DCDFB8A9E3BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C603A1A-A8E9-4EC7-AC03-DCDFB8A9E3BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C603A1A-A8E9-4EC7-AC03-DCDFB8A9E3BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{8FF0D556-CF73-4796-935B-DBA1AFC643C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8FF0D556-CF73-4796-935B-DBA1AFC643C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8FF0D556-CF73-4796-935B-DBA1AFC643C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8FF0D556-CF73-4796-935B-DBA1AFC643C8}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{3B5A0094-670D-4BB1-BFDD-61B88A8773DC} = {CA9AC87F-097E-4F15-8393-4BC07735A5B0}
		{91853F21-9CD9-4132-BC29-A7D5D84FFFE7} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{EF480016-9127-4916-8735-D2466BDBC582} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{27B2DDC7-8B75-4322-A312-25419C15D9D8} = {CA9AC87F-097E-4F15-8393-4BC07735A5B0}
		{EA728778-AF6E-41A9-8A4C-A878A8E51DD0} = {CA9AC87F-097E-4F15-8393-4BC07735A5B0}
		{63B6D95A-C16E-475F-A871-1F97EA5A629C} = {CA9AC87F-097E-4F15-8393-4BC07735A5B0}
		{DF9F3996-3664-40E8-B6C0-1792AFD1BAE3} = {CA9AC87F-097E-4F15-8393-4BC07735A5B0}
		{2C603A1A-A8E9-4EC7-AC03-DCDFB8A9E3BF} = {04DBDB01-70F4-4E06-B468-8F87850B22BE}
		{8FF0D556-CF73-4796-935B-DBA1AFC643C8} = {B6FCF58A-0ABD-4AA3-A3F4-122A18E239E6}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {28315BFD-90E7-4E14-A2EA-F3D23AF4126F}
	EndGlobalSection
EndGlobal
