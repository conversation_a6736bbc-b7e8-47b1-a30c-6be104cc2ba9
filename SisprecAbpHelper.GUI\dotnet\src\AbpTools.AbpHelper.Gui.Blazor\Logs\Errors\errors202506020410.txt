2025-06-02 04:10:47.069 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.070 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.099 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.153 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.214 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.278 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.341 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.402 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.463 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.524 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.586 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.647 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.710 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.773 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.836 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.897 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:47.958 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.020 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.084 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.147 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.211 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.272 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.333 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.396 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.460 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.524 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.588 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.650 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.713 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.777 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.840 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.904 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:48.968 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.031 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.095 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.159 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.220 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.284 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.348 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.410 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.474 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.537 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.599 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.660 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.725 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.788 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.851 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.914 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:49.978 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.041 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.104 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.168 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.232 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.296 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.359 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.423 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.486 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.549 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.614 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.677 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.741 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.805 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.869 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.933 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:50.995 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.057 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.121 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.183 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.246 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.311 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.373 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.437 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.501 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.564 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.625 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.688 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.752 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.815 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.878 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:51.943 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.006 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.070 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.133 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.197 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.260 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.324 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.388 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.451 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.516 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.579 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.643 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.706 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.768 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.832 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.896 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:52.959 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.019 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.087 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.141 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.192 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.203 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.267 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.327 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.389 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.451 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.514 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.577 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.639 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.704 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.767 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.830 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.893 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:53.957 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.020 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.083 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.144 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.207 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.270 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.332 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.396 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.460 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.521 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.585 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.649 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.712 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.776 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.840 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.901 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:54.962 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.027 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.087 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.148 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.212 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.273 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.337 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.398 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.460 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.523 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.583 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.644 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.707 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.770 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.834 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.896 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:55.958 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.022 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.085 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.146 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.210 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.273 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.336 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.397 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.461 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.523 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.585 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.648 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.709 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.772 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.836 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.900 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:56.963 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.023 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.084 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.147 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.211 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.275 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.339 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.402 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.466 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.529 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.593 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.655 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.718 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.782 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.846 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.910 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:57.973 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.036 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.100 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.164 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.229 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.291 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.352 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.415 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.478 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.543 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.606 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.670 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.733 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.797 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.859 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.922 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:58.986 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.049 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.110 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.174 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.235 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.299 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.362 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.427 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.490 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.554 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.617 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.681 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.743 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.805 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.869 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.932 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 04:10:59.994 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
