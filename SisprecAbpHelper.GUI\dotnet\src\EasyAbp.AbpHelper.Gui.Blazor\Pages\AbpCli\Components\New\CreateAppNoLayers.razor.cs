﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.New;
using AbpTools.AbpHelper.Gui.AbpCli.New.Dtos;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.New
{
    public partial class CreateAppNoLayers
    {
        [Inject]
        private IAbpCliNewAppService Service { get; set; }
        
        protected AbpNewAppNoLayersInput Input { get; set; } = new()
        {
            Ui = AppUiFramework.Mvc,
            DatabaseProvider = AppDatabaseProvider.Ef,
            DatabaseManagementSystem = Database.SqlServer,
            Version = AbpVersionHelper.AbpVersion
        };

        protected override async Task InternalExecuteAsync()
        {
            await Service.CreateAppNoLayersAsync(Input);
        }
    }
}
