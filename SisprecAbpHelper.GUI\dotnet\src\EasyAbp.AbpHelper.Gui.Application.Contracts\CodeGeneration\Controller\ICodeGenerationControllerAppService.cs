﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.CodeGeneration.Controller.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Controller
{
    public interface ICodeGenerationControllerAppService : IApplicationService
    {
        Task<ServiceExecutionResult> GenerateAsync(AbpHelperGenerateControllerInput input);
    }
}