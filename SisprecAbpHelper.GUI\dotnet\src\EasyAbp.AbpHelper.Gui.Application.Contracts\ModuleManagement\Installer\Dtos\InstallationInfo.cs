﻿using System.Collections.Generic;

namespace AbpTools.AbpHelper.Gui.ModuleManagement.Installer.Dtos
{
    public class InstallationInfo
    {
        public string ModuleGroupId { get; set; }
        
        public string ModuleId { get; set; }
        
        public string PackageName => ModuleId != "" ? $"{ModuleGroupId}.{ModuleId}" : ModuleGroupId;
        
        public string Submodule { get; set; }

        public List<string> Targets { get; set; }
    }
}