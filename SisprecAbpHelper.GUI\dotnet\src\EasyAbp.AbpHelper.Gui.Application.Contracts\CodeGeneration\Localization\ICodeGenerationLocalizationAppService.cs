﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.CodeGeneration.Localization.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Localization
{
    public interface ICodeGenerationLocalizationAppService : IApplicationService
    {
        Task<ServiceExecutionResult> GenerateItemsAsync(AbpHelperGenerateLocalizationItemsInput input);
    }
}