﻿using System;
using AbpTools.AbpHelper.Gui.CodeGeneration.Shared.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using JetBrains.Annotations;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Migration.Dtos
{
    [Serializable]
    public class AbpHelperGenerateMigrationRemoveInput : AbpHelperInput
    {
        [CanBeNull]
        public virtual string EfOptions { get; set; }

        [CanBeNull]
        public virtual string MigrationProjectName { get; set; }


        public AbpHelperGenerateMigrationRemoveInput()
        {
        }

        public AbpHelperGenerateMigrationRemoveInput([NotNull] string directory, [CanBeNull] string projectName,
            [CanBeNull] string exclude, [CanBeNull] string efOptions, [CanBeNull] string migrationProjectName) : base(
            directory, projectName, exclude)
        {
            EfOptions = efOptions;
            MigrationProjectName = migrationProjectName;
        }
    }
}