<Project Sdk="Microsoft.NET.Sdk">
    <Import Project="..\..\common.props" />
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>AbpTools.AbpHelper.Gui</RootNamespace>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Volo.Abp.TestBase"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Autofac"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Authorization"
                          Version="$(AbpVersion)" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk"
                          Version="17.11.1" />
        <PackageReference Include="NSubstitute"
                          Version="5.1.0" />
        <PackageReference Include="Shouldly"
                          Version="4.2.1" />
        <PackageReference Include="xunit"
                          Version="2.9.2" />
        <PackageReference Include="xunit.extensibility.execution"
                          Version="2.9.2" />
        <PackageReference Include="xunit.runner.visualstudio"
                          Version="2.8.2" />
    </ItemGroup>
</Project>