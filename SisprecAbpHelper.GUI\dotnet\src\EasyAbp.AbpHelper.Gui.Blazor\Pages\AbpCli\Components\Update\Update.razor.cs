﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Update;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Update
{
    public partial class Update
    {
        [Inject]
        private IAbpCliUpdateAppService Service { get; set; }

        public Update()
        {
            Input.Version = AbpVersionHelper.AbpVersion;
        }

        protected override async Task InternalExecuteAsync()
        {
            await Service.UpdateAsync(Input);
        }
    }
}