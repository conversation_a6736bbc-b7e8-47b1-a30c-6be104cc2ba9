﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.New.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.AbpCli.New
{
    public interface IAbpCliNewAppService : IApplicationService
    {
        Task<ServiceExecutionResult> CreateAppAsync(AbpNewAppInput input);

        Task<ServiceExecutionResult> CreateAppNoLayersAsync(AbpNewAppNoLayersInput input);

        Task<ServiceExecutionResult> CreateModuleAsync(AbpNewModuleInput input);

        Task<ServiceExecutionResult> CreateConsoleAsync(AbpNewConsoleInput input);

        Task<ServiceExecutionResult> CreateMauiAsync(AbpNewMauiInput input);
    }
}