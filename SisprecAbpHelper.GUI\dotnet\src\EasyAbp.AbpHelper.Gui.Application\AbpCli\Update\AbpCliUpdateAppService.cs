﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Update.Dtos;
using AbpTools.AbpHelper.Gui.Common;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Cli.Commands;

namespace AbpTools.AbpHelper.Gui.AbpCli.Update
{
    public class AbpCliUpdateAppService : AbpCliAppService, IAbpCliUpdateAppService
    {
        private readonly UpdateCommand _updateCommand;
        private readonly ICurrentDirectoryHelper _currentDirectoryHelper;

        public AbpCliUpdateAppService(
            UpdateCommand updateCommand,
            ICurrentDirectoryHelper currentDirectoryHelper)
        {
            _updateCommand = updateCommand;
            _currentDirectoryHelper = currentDirectoryHelper;
        }

        public async Task<ServiceExecutionResult> UpdateAsync(AbpUpdateInput input)
        {
            var args = CreateCommandLineArgs(input, "abp update");

            using (_currentDirectoryHelper.Change(input.Directory))
            {
                await _updateCommand.ExecuteAsync(args);
            }

            return new ServiceExecutionResult(true);
        }
    }
}