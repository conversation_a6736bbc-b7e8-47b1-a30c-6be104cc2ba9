﻿@using Volo.Abp.AspNetCore.Components.Web
@using AbpTools.AbpHelper.Gui.Localization
@using AbpTools.AbpHelper.Gui.AbpCli.Bundle.Dtos;
@using AbpTools.AbpHelper.Gui.Blazor.Pages.Shared;
@inherits ExecutableComponentBaseWithDirectory<AbpBundleInput>
@inject AbpBlazorMessageLocalizerHelper<GuiResource> Lh

<Card Class="mb-4">
    <CardBody>
        <Alert Color="Color.Info"  Visible>
            <AlertDescription>
                <Icon Name="IconName.QuestionCircle" /> @L["Info:FunctionIsBasedOnAbpCliPart1"]
            </AlertDescription>
            <AlertMessage>
                <Icon Name="IconName.ExternalLinkSquareAlt"/>
                <Link To="https://docs.abp.io/en/abp/latest/CLI#bundle" Target="Target.Blank" Style="text-decoration: none">
                    @L["Info:FunctionIsBasedOnAbpCliPart2_Document"]
                </Link>
            </AlertMessage>
            <AlertDescription>
                @L["Info:FunctionIsBasedOnAbpCliPart3"]
            </AlertDescription>
        </Alert>
        <Form>
            <Validations @ref="ValidationsRef" Model="Input" ValidateOnLoad="false">
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["Directory"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Directory">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.Force">@L["AbpCli_Bundle:Force"]</Check>
                </Field>
                <Field>
                    <FieldLabel>@L["AbpCli_Bundle:ProjectType"]</FieldLabel>
                    <Select TValue="ProjectType" @bind-SelectedValue="Input.ProjectType">
                        @foreach (var projectType in Enum.GetValues<ProjectType>())
                        {
                            <SelectItem Value="projectType">
                                @L[$"AbpCli_Bundle:ProjectType:{Enum.GetName(projectType)}"]
                            </SelectItem>
                        }
                    </Select>
                </Field>

                <SubmitButton Block="true" Clicked="@ExecuteAsync" SaveResourceKey="Button:Execute" />
            </Validations>
        </Form>
    </CardBody>
</Card>