<Project Sdk="Microsoft.NET.Sdk">
    <Import Project="..\..\common.props" />
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>AbpTools.AbpHelper.Gui</RootNamespace>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Volo.Abp.AutoMapper"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Cli.Core"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Ddd.Application"
                          Version="$(AbpVersion)" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\AbpHelper.CLI\src\AbpHelper.Core\AbpHelper.Core.csproj" />
        <ProjectReference Include="..\AbpTools.AbpHelper.Gui.Application.Contracts\AbpTools.AbpHelper.Gui.Application.Contracts.csproj" />
    </ItemGroup>
</Project>