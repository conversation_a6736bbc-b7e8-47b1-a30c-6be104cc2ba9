﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.CodeGeneration.Migration.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Migration
{
    public interface ICodeGenerationMigrationAppService : IApplicationService
    {
        Task<ServiceExecutionResult> AddAsync(AbpHelperGenerateMigrationAddInput input);
        
        Task<ServiceExecutionResult> RemoveAsync(AbpHelperGenerateMigrationRemoveInput input);
    }
}