﻿@using Volo.Abp.AspNetCore.Components.Web
@using AbpTools.AbpHelper.Gui.Localization
@using AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos;
@using AbpTools.AbpHelper.Gui.Blazor.Pages.Shared;
@inherits ExecutableComponentBaseWithDirectory<AbpGenerateRemoveCSharpProxyInput>
@inject AbpBlazorMessageLocalizerHelper<GuiResource> Lh

<Card Class="mb-4">
    <CardBody>
        <Alert Color="Color.Info"  Visible>
            <AlertDescription>
                <Icon Name="IconName.QuestionCircle" /> @L["Info:FunctionIsBasedOnAbpCliPart1"]
            </AlertDescription>
            <AlertMessage>
                <Icon Name="IconName.ExternalLinkSquareAlt"/>
                <Link To="https://docs.abp.io/en/abp/latest/CLI#generate-proxy" Target="Target.Blank" Style="text-decoration: none">
                    @L["Info:FunctionIsBasedOnAbpCliPart2_Document"]
                </Link>
            </AlertMessage>
            <AlertDescription>
                @L["Info:FunctionIsBasedOnAbpCliPart3"]
            </AlertDescription>
        </Alert>
        <Form>
            <Validations @ref="ValidationsRef" Model="Input" ValidateOnLoad="false">
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["Directory"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Directory" Autofocus="true">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_Proxy:Url"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Url">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_Proxy:Module"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Module">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_Proxy:Folder"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Folder">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.WithoutContracts">@L["AbpCli_Proxy:WithoutContracts"]</Check>
                </Field>
                <Field>
                    <FieldLabel>@L["AbpCli_Proxy:ServiceType"]</FieldLabel>
                    <Select TValue="ServiceType" @bind-SelectedValue="Input.ServiceType">
                        @foreach (var serviceType in Enum.GetValues<ServiceType>())
                        {
                            <SelectItem Value="serviceType">
                                @L[$"AbpCli_Proxy:ServiceType:{Enum.GetName(serviceType)}"]
                            </SelectItem>
                        }
                    </Select>
                </Field>

                <SubmitButton Block="true" Clicked="@ExecuteAsync" SaveResourceKey="Button:GenerateProxy" />
                <SubmitButton Block="true" Clicked="@ExecuteRemoveAsync" SaveResourceKey="Button:RemoveProxy" Color="Color.Warning" />
            </Validations>
        </Form>
    </CardBody>
</Card>