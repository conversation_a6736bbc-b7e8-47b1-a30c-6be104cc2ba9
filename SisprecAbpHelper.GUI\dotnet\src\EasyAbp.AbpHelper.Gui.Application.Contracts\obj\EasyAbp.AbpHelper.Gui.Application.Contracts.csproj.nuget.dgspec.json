{"format": 1, "restore": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj": {}}, "projects": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj": {"version": "2.16.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj", "projectName": "EasyAbp.AbpHelper.Gui.Application.Contracts", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[9.0.0, )"}, "Volo.Abp.Account.Application.Contracts": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Identity.Application.Contracts": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.ObjectExtending": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}