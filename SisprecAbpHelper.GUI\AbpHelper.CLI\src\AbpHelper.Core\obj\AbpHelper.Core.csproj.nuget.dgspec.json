{"format": 1, "restore": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj": {}}, "projects": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj", "projectName": "AbpTools.AbpHelper.Core", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Bogus": {"target": "Package", "version": "[35.5.1, )"}, "ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.2, )"}, "Elsa": {"target": "Package", "version": "[1.2.2.29, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.6.0, )"}, "Humanizer.Core": {"target": "Package", "version": "[2.14.1, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[4.5.0, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[8.0.0, )"}, "NuGet.Protocol": {"target": "Package", "version": "[6.2.2, )"}, "Scriban": {"target": "Package", "version": "[5.10.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "SourceLink.Create.CommandLine": {"suppressParent": "All", "target": "Package", "version": "[2.8.3, )"}, "System.CommandLine": {"target": "Package", "version": "[2.0.0-beta1.20371.2, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Core": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Http": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.VirtualFileSystem": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}