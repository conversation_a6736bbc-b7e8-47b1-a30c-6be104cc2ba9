<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>10.0</LangVersion>
    <Nullable>enable</Nullable>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>abphelper</ToolCommandName>
    <PackageId>AbpTools.AbpHelper</PackageId>
    <AssemblyName>AbpTools.AbpHelper</AssemblyName>
    <RootNamespace>AbpTools.AbpHelper</RootNamespace>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\AbpHelper.Core\AbpHelper.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Fody" Version="6.6.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>
</Project>
