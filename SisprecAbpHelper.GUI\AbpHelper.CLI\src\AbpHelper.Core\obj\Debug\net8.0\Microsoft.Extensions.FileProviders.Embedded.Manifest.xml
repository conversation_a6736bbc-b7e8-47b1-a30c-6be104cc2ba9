﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?><Manifest><ManifestVersion>1.0</ManifestVersion><FileSystem><File Name="Microsoft.Extensions.FileProviders.Embedded.Manifest.xml"><ResourcePath>Microsoft.Extensions.FileProviders.Embedded.Manifest.xml</ResourcePath></File><Directory Name="Templates"><Directory Name="Controller"><File Name="ControllerMethod"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Controller.ControllerMethod</ResourcePath></File><Directory Name="Groups"><Directory Name="Controller"><Directory Name="src"><Directory Name="{{ProjectInfo.FullName}}.HttpApi"><Directory Name="{{InterfaceInfo.RelativeDirectory}}"><File Name="{{Option.Name}}Controller.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Controller.Groups.Controller.src.__ProjectInfo.FullName__.HttpApi.__InterfaceInfo.RelativeDirectory__.{{Option.Name}}Controller.cs</ResourcePath></File><File Name="{{Option.Name}}IntegrationController.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Controller.Groups.Controller.src.__ProjectInfo.FullName__.HttpApi.__InterfaceInfo.RelativeDirectory__.{{Option.Name}}IntegrationController.cs</ResourcePath></File></Directory></Directory></Directory></Directory></Directory></Directory><Directory Name="Crud"><File Name="AppRoutingModule_ImportApplicationLayoutComponent"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.AppRoutingModule_ImportApplicationLayoutComponent</ResourcePath></File><File Name="AppRoutingModule_Routing"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.AppRoutingModule_Routing</ResourcePath></File><File Name="ApplicationAutoMapperProfile_CreateMap"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.ApplicationAutoMapperProfile_CreateMap</ResourcePath></File><File Name="DbContextClass_Property"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.DbContextClass_Property</ResourcePath></File><File Name="DbContextInterface_Property"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.DbContextInterface_Property</ResourcePath></File><File Name="DbContextInterface_Using"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.DbContextInterface_Using</ResourcePath></File><File Name="DbContextModelCreatingExtensions_EntityConfig"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.DbContextModelCreatingExtensions_EntityConfig</ResourcePath></File><File Name="DbContextModelCreatingExtensions_Using"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.DbContextModelCreatingExtensions_Using</ResourcePath></File><File Name="EntityFrameworkCoreModule_AddRepository"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.EntityFrameworkCoreModule_AddRepository</ResourcePath></File><File Name="Entity_ProtectedConstructor"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Entity_ProtectedConstructor</ResourcePath></File><File Name="Entity_PublicConstructor"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Entity_PublicConstructor</ResourcePath></File><Directory Name="Groups"><Directory Name="Repository"><Directory Name="src"><Directory Name="{{ProjectInfo.FullName}}.Domain"><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="I{{EntityInfo.Name}}Manager.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Repository.src.__ProjectInfo.FullName__.Domain.__EntityInfo.RelativeDirectory__.I{{EntityInfo.Name}}Manager.cs</ResourcePath></File><File Name="I{{EntityInfo.Name}}Repository.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Repository.src.__ProjectInfo.FullName__.Domain.__EntityInfo.RelativeDirectory__.I{{EntityInfo.Name}}Repository.cs</ResourcePath></File><Directory Name="Servicos"><File Name="{{EntityInfo.Name}}Manager.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Repository.src.__ProjectInfo.FullName__.Domain.__EntityInfo.RelativeDirectory__.Servicos.{{EntityInfo.Name}}Manager.cs</ResourcePath></File></Directory></Directory></Directory><Directory Name="{{ProjectInfo.FullName}}.EntityFrameworkCore"><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="{{EntityInfo.Name}}EfCoreQueryableExtensions.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Repository.src.__ProjectInfo.FullName__.EntityFrameworkCore.__EntityInfo.RelativeDirectory__.{{EntityInfo.Name}}EfCoreQueryableExtensions.cs</ResourcePath></File><File Name="{{EntityInfo.Name}}Repository.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Repository.src.__ProjectInfo.FullName__.EntityFrameworkCore.__EntityInfo.RelativeDirectory__.{{EntityInfo.Name}}Repository.cs</ResourcePath></File></Directory></Directory></Directory></Directory><Directory Name="Service"><Directory Name="src"><Directory Name="{{ProjectInfo.FullName}}.Application"><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="{{EntityInfo.Name}}AppService.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Service.src.__ProjectInfo.FullName__.Application.__EntityInfo.RelativeDirectory__.{{EntityInfo.Name}}AppService.cs</ResourcePath></File></Directory></Directory><Directory Name="{{ProjectInfo.FullName}}.Application.Contracts"><Directory Name="{{EntityInfo.RelativeDirectory}}"><Directory Name="Dtos"><File Name="{{ EntityInfo.Name }}GetListInput.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__EntityInfo.RelativeDirectory__.Dtos.{{ EntityInfo.Name }}GetListInput.cs</ResourcePath></File><File Name="{{DtoInfo.CreateTypeName}}.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__EntityInfo.RelativeDirectory__.Dtos.{{DtoInfo.CreateTypeName}}.cs</ResourcePath></File><File Name="{{DtoInfo.ReadTypeName}}.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__EntityInfo.RelativeDirectory__.Dtos.{{DtoInfo.ReadTypeName}}.cs</ResourcePath></File><File Name="{{DtoInfo.UpdateTypeName}}.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__EntityInfo.RelativeDirectory__.Dtos.{{DtoInfo.UpdateTypeName}}.cs</ResourcePath></File><File Name="{{EntityInfo.CompositeKeyName}}.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__EntityInfo.RelativeDirectory__.Dtos.{{EntityInfo.CompositeKeyName}}.cs</ResourcePath></File></Directory><File Name="I{{EntityInfo.Name}}AppService.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__EntityInfo.RelativeDirectory__.I{{EntityInfo.Name}}AppService.cs</ResourcePath></File></Directory></Directory></Directory></Directory><Directory Name="Test"><Directory Name="test"><Directory Name="{{ProjectInfo.FullName}}.Application.Tests"><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="{{EntityInfo.Name}}AppServiceTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.Application.Tests.__EntityInfo.RelativeDirectory__.{{EntityInfo.Name}}AppServiceTests.cs</ResourcePath></File></Directory></Directory><Directory Name="{{ProjectInfo.FullName}}.Domain.Tests"><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="{{EntityInfo.Name}}DomainTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.Domain.Tests.__EntityInfo.RelativeDirectory__.{{EntityInfo.Name}}DomainTests.cs</ResourcePath></File><File Name="{{EntityInfo.Name}}ManagerTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.Domain.Tests.__EntityInfo.RelativeDirectory__.{{EntityInfo.Name}}ManagerTests.cs</ResourcePath></File></Directory></Directory><Directory Name="{{ProjectInfo.FullName}}.EntityFrameworkCore.Tests"><Directory Name="EntityFrameworkCore"><Directory Name="Applications"><File Name="{{EntityInfo.Name}}AppServiceTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.EntityFrameworkCore.Tests.EntityFrameworkCore.Applications.{{EntityInfo.Name}}AppServiceTests.cs</ResourcePath></File></Directory><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="{{EntityInfo.Name}}RepositoryTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.EntityFrameworkCore.Tests.EntityFrameworkCore.__EntityInfo.RelativeDirectory__.{{EntityInfo.Name}}RepositoryTests.cs</ResourcePath></File></Directory></Directory></Directory><Directory Name="{{ProjectInfo.FullName}}.Web.Tests"><Directory Name="Pages"><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="CreateModalTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.Web.Tests.Pages.__EntityInfo.RelativeDirectory__.CreateModalTests.cs</ResourcePath></File><File Name="DetalheModalTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.Web.Tests.Pages.__EntityInfo.RelativeDirectory__.DetalheModalTests.cs</ResourcePath></File><File Name="EditModalTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.Web.Tests.Pages.__EntityInfo.RelativeDirectory__.EditModalTests.cs</ResourcePath></File><File Name="IndexTests.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.Test.test.__ProjectInfo.FullName__.Web.Tests.Pages.__EntityInfo.RelativeDirectory__.IndexTests.cs</ResourcePath></File></Directory></Directory></Directory></Directory></Directory><Directory Name="UiAngular"><Directory Name="angular"><Directory Name="src"><Directory Name="app"><Directory Name="store"><Directory Name="models"><File Name="{{abp.camel_case EntityInfo.NamespaceLastPart}}.ts"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiAngular.angular.src.app.store.models.{{abp.camel_case EntityInfo.NamespaceLastPart}}.ts</ResourcePath></File></Directory></Directory></Directory></Directory></Directory></Directory><Directory Name="UiRazor"><Directory Name="src"><Directory Name="{{ProjectInfo.FullName}}.Web"><Directory Name="Pages"><Directory Name="{{Bag.PagesFolder}}"><Directory Name="{{EntityInfo.RelativeDirectory}}"><File Name="CreateModal.cshtml"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.CreateModal.cshtml</ResourcePath></File><File Name="CreateModal.cshtml.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.CreateModal.cshtml.cs</ResourcePath></File><File Name="DetalheModal.cshtml"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.DetalheModal.cshtml</ResourcePath></File><File Name="DetalheModal.cshtml.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.DetalheModal.cshtml.cs</ResourcePath></File><File Name="EditModal.cshtml"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.EditModal.cshtml</ResourcePath></File><File Name="EditModal.cshtml.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.EditModal.cshtml.cs</ResourcePath></File><File Name="Index.cshtml"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.Index.cshtml</ResourcePath></File><File Name="Index.cshtml.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.Index.cshtml.cs</ResourcePath></File><Directory Name="ViewModels"><File Name="CreateEdit{{EntityInfo.Name}}ViewModel.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.ViewModels.CreateEdit{{EntityInfo.Name}}ViewModel.cs</ResourcePath></File><File Name="Create{{EntityInfo.Name}}ViewModel.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.ViewModels.Create{{EntityInfo.Name}}ViewModel.cs</ResourcePath></File><File Name="Detalhe{{EntityInfo.Name}}ViewModel.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.ViewModels.Detalhe{{EntityInfo.Name}}ViewModel.cs</ResourcePath></File><File Name="Edit{{EntityInfo.Name}}ViewModel.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.ViewModels.Edit{{EntityInfo.Name}}ViewModel.cs</ResourcePath></File></Directory><File Name="index.css"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.index.css</ResourcePath></File><File Name="index.js"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Groups.UiRazor.src.__ProjectInfo.FullName__.Web.Pages.__Bag.PagesFolder__.__EntityInfo.RelativeDirectory__.index.js</ResourcePath></File></Directory></Directory></Directory></Directory></Directory></Directory></Directory><File Name="Localization"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Localization</ResourcePath></File><File Name="MenuContributor_AddMenuItem"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.MenuContributor_AddMenuItem</ResourcePath></File><File Name="MenuContributor_AuthorizationService"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.MenuContributor_AuthorizationService</ResourcePath></File><File Name="MenuContributor_ConfigureMainMenu"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.MenuContributor_ConfigureMainMenu</ResourcePath></File><File Name="MenuContributor_Localizer"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.MenuContributor_Localizer</ResourcePath></File><File Name="MenuContributor_UsingForApp"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.MenuContributor_UsingForApp</ResourcePath></File><File Name="MenuContributor_UsingForModule"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.MenuContributor_UsingForModule</ResourcePath></File><File Name="Menus_AddMenuName"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Menus_AddMenuName</ResourcePath></File><File Name="Module_ImportSharedModule"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Module_ImportSharedModule</ResourcePath></File><File Name="Module_SharedModule"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Module_SharedModule</ResourcePath></File><File Name="Permissions_AddGroup"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Permissions_AddGroup</ResourcePath></File><File Name="Permissions_Definitions"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Permissions_Definitions</ResourcePath></File><File Name="Permissions_Names"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.Permissions_Names</ResourcePath></File><File Name="RoutingModule_ImportList"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.RoutingModule_ImportList</ResourcePath></File><File Name="RoutingModule_Routes"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.RoutingModule_Routes</ResourcePath></File><File Name="UsingEntityDtoNamespace"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.UsingEntityDtoNamespace</ResourcePath></File><File Name="UsingEntityNamespace"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.UsingEntityNamespace</ResourcePath></File><File Name="WebAutoMapperProfile_CreateMap"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.WebAutoMapperProfile_CreateMap</ResourcePath></File><File Name="WebAutoMapperProfile_Using"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Crud.WebAutoMapperProfile_Using</ResourcePath></File></Directory><Directory Name="Localization"><File Name="Localization"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Localization.Localization</ResourcePath></File></Directory><Directory Name="Methods"><File Name="AppServiceClass"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Methods.AppServiceClass</ResourcePath></File><File Name="AppServiceInterface"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Methods.AppServiceInterface</ResourcePath></File><File Name="AppService_UsingDto"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Methods.AppService_UsingDto</ResourcePath></File><File Name="AppService_UsingTask"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Methods.AppService_UsingTask</ResourcePath></File><Directory Name="Groups"><Directory Name="Service"><Directory Name="src"><Directory Name="{{ProjectInfo.FullName}}.Application.Contracts"><Directory Name="{{InterfaceInfo.RelativeDirectory}}"><Directory Name="Dtos"><File Name="{{Bag.Name}}Input.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Methods.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__InterfaceInfo.RelativeDirectory__.Dtos.{{Bag.Name}}Input.cs</ResourcePath></File><File Name="{{Bag.Name}}Output.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Methods.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__InterfaceInfo.RelativeDirectory__.Dtos.{{Bag.Name}}Output.cs</ResourcePath></File></Directory></Directory></Directory></Directory></Directory></Directory></Directory><Directory Name="Module"><File Name="MigrationsContext_ConfigureModule"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Module.MigrationsContext_ConfigureModule</ResourcePath></File><File Name="MigrationsContext_Using"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Module.MigrationsContext_Using</ResourcePath></File><File Name="ModuleClass_DependsOn"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Module.ModuleClass_DependsOn</ResourcePath></File><File Name="ModuleClass_Using"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Module.ModuleClass_Using</ResourcePath></File></Directory><Directory Name="Service"><Directory Name="Groups"><Directory Name="Service"><Directory Name="src"><Directory Name="{{ProjectInfo.FullName}}.Application"><Directory Name="{{Option.Folder}}"><File Name="{{Option.Name}}AppService.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Service.Groups.Service.src.__ProjectInfo.FullName__.Application.__Option.Folder__.{{Option.Name}}AppService.cs</ResourcePath></File></Directory></Directory><Directory Name="{{ProjectInfo.FullName}}.Application.Contracts"><Directory Name="{{Option.Folder}}"><File Name="I{{Option.Name}}AppService.cs"><ResourcePath>AbpTools.AbpHelper.Core.Templates.Service.Groups.Service.src.__ProjectInfo.FullName__.Application.Contracts.__Option.Folder__.I{{Option.Name}}AppService.cs</ResourcePath></File></Directory></Directory></Directory></Directory></Directory></Directory></Directory></FileSystem></Manifest>