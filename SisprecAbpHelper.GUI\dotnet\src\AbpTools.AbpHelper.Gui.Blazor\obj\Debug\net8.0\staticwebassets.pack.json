{"Files": [{"Id": "C:\\Users\\<USER>\\.nuget\\packages\\blazorx-analytics\\1.0.0\\contentFiles\\any\\netstandard2.0\\wwwroot\\blazor-analytics.js", "PackagePath": "staticwebassets\\blazor-analytics.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AbpTools.AbpHelper.Gui.Blazor.bundle.scp.css", "PackagePath": "staticwebassets\\AbpTools.AbpHelper.Gui.Blazor.bundle.scp.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\blazor-global-styles.css", "PackagePath": "staticwebassets\\blazor-global-styles.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\global-styles.css", "PackagePath": "staticwebassets\\global-styles.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\images\\app-icon\\icon-lpx.png", "PackagePath": "staticwebassets\\images\\app-icon\\icon-lpx.png"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\images\\app-icon\\icon-lpx.psd", "PackagePath": "staticwebassets\\images\\app-icon\\icon-lpx.psd"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\images\\app-icon\\icon.png", "PackagePath": "staticwebassets\\images\\app-icon\\icon.png"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\images\\app-icon\\icon.svg", "PackagePath": "staticwebassets\\images\\app-icon\\icon.svg"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\css\\all.css", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\css\\all.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\css\\v4-shims.css", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\css\\v4-shims.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-brands-400.ttf", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-brands-400.ttf"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-brands-400.woff2", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-brands-400.woff2"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-regular-400.ttf", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-regular-400.ttf"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-regular-400.woff2", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-regular-400.woff2"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-solid-900.ttf", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-solid-900.ttf"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-solid-900.woff2", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-solid-900.woff2"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-v4compatibility.ttf", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-v4compatibility.ttf"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-v4compatibility.woff2", "PackagePath": "staticwebassets\\libs\\@fortawesome\\fontawesome-free\\webfonts\\fa-v4compatibility.woff2"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\core\\abp.css", "PackagePath": "staticwebassets\\libs\\abp\\core\\abp.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\core\\abp.js", "PackagePath": "staticwebassets\\libs\\abp\\core\\abp.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\jquery\\abp.jquery.js", "PackagePath": "staticwebassets\\libs\\abp\\jquery\\abp.jquery.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\luxon\\abp.luxon.js", "PackagePath": "staticwebassets\\libs\\abp\\luxon\\abp.luxon.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\utils\\abp-utils.umd.js", "PackagePath": "staticwebassets\\libs\\abp\\utils\\abp-utils.umd.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\utils\\abp-utils.umd.js.map", "PackagePath": "staticwebassets\\libs\\abp\\utils\\abp-utils.umd.js.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\utils\\abp-utils.umd.min.js", "PackagePath": "staticwebassets\\libs\\abp\\utils\\abp-utils.umd.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\abp\\utils\\abp-utils.umd.min.js.map", "PackagePath": "staticwebassets\\libs\\abp\\utils\\abp-utils.umd.min.js.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\bootstrap-datepicker.css.map", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\bootstrap-datepicker.css.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\bootstrap-datepicker.min.css", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\bootstrap-datepicker.min.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\bootstrap-datepicker.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\bootstrap-datepicker.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker-en-CA.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker-en-CA.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ar-DZ.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ar-DZ.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ar-tn.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ar-tn.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ar.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ar.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.az.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.az.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bg.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bg.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bm.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bm.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bn.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bn.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.br.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.br.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bs.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.bs.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ca.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ca.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.cs.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.cs.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.cy.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.cy.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.da.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.da.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.de.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.de.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.el.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.el.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-AU.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-AU.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-CA.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-CA.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-GB.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-GB.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-IE.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-IE.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-NZ.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-NZ.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-US.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-US.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-ZA.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.en-ZA.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.eo.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.eo.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.es.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.es.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.et.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.et.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.eu.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.eu.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fa.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fa.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fi.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fi.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fo.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fo.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fr-CH.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fr-CH.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fr.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.fr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.gl.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.gl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.he.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.he.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hi.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hi.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hr.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hu.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hu.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hy.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.hy.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.id.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.id.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.is.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.is.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.it-CH.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.it-CH.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.it.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.it.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ja.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ja.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ka.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ka.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.kh.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.kh.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.kk.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.kk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.km.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.km.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ko.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ko.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.kr.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.kr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.lt.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.lt.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.lv.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.lv.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.me.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.me.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.mk.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.mk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.mn.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.mn.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.mr.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.mr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ms.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ms.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.nl-BE.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.nl-BE.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.nl.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.nl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.no.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.no.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.oc.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.oc.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.pl.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.pl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.pt-BR.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.pt-BR.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.pt.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.pt.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ro.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ro.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.rs-latin.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.rs-latin.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.rs.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.rs.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ru.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ru.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.si.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.si.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sk.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sl.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sq.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sq.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sr-latin.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sr-latin.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sr.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sv.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sv.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sw.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.sw.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ta.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.ta.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.tg.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.tg.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.th.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.th.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.tk.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.tk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.tr.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.tr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.uk.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.uk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.uz-cyrl.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.uz-cyrl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.uz-latn.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.uz-latn.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.vi.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.vi.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.zh-CN.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.zh-CN.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.zh-TW.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap-datepicker\\locales\\bootstrap-datepicker.zh-TW.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-daterangepicker\\daterangepicker.css", "PackagePath": "staticwebassets\\libs\\bootstrap-daterangepicker\\daterangepicker.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap-daterangepicker\\daterangepicker.js", "PackagePath": "staticwebassets\\libs\\bootstrap-daterangepicker\\daterangepicker.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.css", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.css.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.min.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.min.css.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.rtl.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.rtl.css.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.rtl.min.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\libs\\bootstrap\\css\\bootstrap.rtl.min.css.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\libs\\bootstrap\\js\\bootstrap.bundle.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\libs\\bootstrap\\js\\bootstrap.bundle.js.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\libs\\bootstrap\\js\\bootstrap.bundle.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\libs\\bootstrap\\js\\bootstrap.bundle.min.js.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\js\\bootstrap.enable.popovers.everywhere.js", "PackagePath": "staticwebassets\\libs\\bootstrap\\js\\bootstrap.enable.popovers.everywhere.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\bootstrap\\js\\bootstrap.enable.tooltips.everywhere.js", "PackagePath": "staticwebassets\\libs\\bootstrap\\js\\bootstrap.enable.tooltips.everywhere.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\datatables.net-bs5\\css\\dataTables.bootstrap5.css", "PackagePath": "staticwebassets\\libs\\datatables.net-bs5\\css\\dataTables.bootstrap5.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\datatables.net-bs5\\js\\dataTables.bootstrap5.js", "PackagePath": "staticwebassets\\libs\\datatables.net-bs5\\js\\dataTables.bootstrap5.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\datatables.net\\js\\dataTables.min.js", "PackagePath": "staticwebassets\\libs\\datatables.net\\js\\dataTables.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-form\\jquery.form.min.js", "PackagePath": "staticwebassets\\libs\\jquery-form\\jquery.form.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\libs\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\jquery.validate.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\jquery.validate.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ar.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ar.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ar.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ar.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_az.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_az.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_az.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_az.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_bg.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_bg.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_bg.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_bg.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_bn_BD.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_bn_BD.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_bn_BD.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_bn_BD.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ca.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ca.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ca.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ca.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_cs.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_cs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_cs.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_cs.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_da.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_da.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_da.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_da.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_de.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_de.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_de.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_de.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_el.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_el.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_el.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_el.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_es.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_es.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_es.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_es.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_es_AR.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_es_AR.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_es_AR.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_es_AR.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_es_PE.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_es_PE.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_es_PE.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_es_PE.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_et.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_et.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_et.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_et.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_eu.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_eu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_eu.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_eu.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_fa.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_fa.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_fa.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_fa.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_fi.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_fi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_fi.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_fi.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_fr.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_fr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_fr.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_fr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ge.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ge.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ge.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ge.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_gl.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_gl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_gl.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_gl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_he.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_he.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_he.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_he.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hi.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hi.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hi.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hr.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hr.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hu.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hu.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hu.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hy_AM.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hy_AM.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_hy_AM.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_hy_AM.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_id.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_id.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_id.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_id.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_is.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_is.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_is.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_is.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_it.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_it.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_it.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_it.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ja.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ja.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ja.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ja.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ka.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ka.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ka.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ka.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_kk.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_kk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_kk.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_kk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ko.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ko.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ko.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ko.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_lt.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_lt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_lt.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_lt.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_lv.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_lv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_lv.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_lv.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_mk.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_mk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_mk.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_mk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_my.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_my.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_my.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_my.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_nl.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_nl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_nl.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_nl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_no.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_no.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_no.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_no.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_pl.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_pl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_pl.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_pl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_pt_BR.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_pt_BR.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_pt_BR.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_pt_BR.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_pt_PT.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_pt_PT.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_pt_PT.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_pt_PT.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ro.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ro.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ro.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ro.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ru.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ru.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ru.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ru.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sd.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sd.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sd.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sd.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_si.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_si.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_si.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_si.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sk.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sk.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sl.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sl.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sr.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sr.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sr_lat.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sr_lat.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sr_lat.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sr_lat.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sv.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_sv.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_sv.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_th.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_th.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_th.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_th.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_tj.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_tj.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_tj.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_tj.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_tr.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_tr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_tr.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_tr.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_uk.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_uk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_uk.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_uk.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ur.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ur.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_ur.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_ur.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_vi.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_vi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_vi.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_vi.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_zh.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_zh.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_zh.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_zh.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_zh_TW.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_zh_TW.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\messages_zh_TW.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\messages_zh_TW.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_de.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_de.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_de.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_de.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_es_CL.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_es_CL.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_es_CL.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_es_CL.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_fi.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_fi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_fi.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_fi.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_it.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_it.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_it.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_it.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_nl.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_nl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_nl.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_nl.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_pt.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_pt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery-validation\\localization\\methods_pt.min.js", "PackagePath": "staticwebassets\\libs\\jquery-validation\\localization\\methods_pt.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\jquery\\jquery.js", "PackagePath": "staticwebassets\\libs\\jquery\\jquery.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\lodash\\lodash.min.js", "PackagePath": "staticwebassets\\libs\\lodash\\lodash.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\luxon\\luxon.js", "PackagePath": "staticwebassets\\libs\\luxon\\luxon.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\luxon\\luxon.js.map", "PackagePath": "staticwebassets\\libs\\luxon\\luxon.js.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\luxon\\luxon.min.js", "PackagePath": "staticwebassets\\libs\\luxon\\luxon.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\luxon\\luxon.min.js.map", "PackagePath": "staticwebassets\\libs\\luxon\\luxon.min.js.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\malihu-custom-scrollbar-plugin\\jquery.mCustomScrollbar.concat.min.js", "PackagePath": "staticwebassets\\libs\\malihu-custom-scrollbar-plugin\\jquery.mCustomScrollbar.concat.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\malihu-custom-scrollbar-plugin\\jquery.mCustomScrollbar.css", "PackagePath": "staticwebassets\\libs\\malihu-custom-scrollbar-plugin\\jquery.mCustomScrollbar.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\malihu-custom-scrollbar-plugin\\jquery.mCustomScrollbar.js", "PackagePath": "staticwebassets\\libs\\malihu-custom-scrollbar-plugin\\jquery.mCustomScrollbar.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\malihu-custom-scrollbar-plugin\\mCSB_buttons.png", "PackagePath": "staticwebassets\\libs\\malihu-custom-scrollbar-plugin\\mCSB_buttons.png"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\malihu-custom-scrollbar-plugin\\package.json", "PackagePath": "staticwebassets\\libs\\malihu-custom-scrollbar-plugin\\package.json"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\malihu-custom-scrollbar-plugin\\readme.md", "PackagePath": "staticwebassets\\libs\\malihu-custom-scrollbar-plugin\\readme.md"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\af.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\af.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar-dz.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar-dz.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar-kw.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar-kw.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar-ly.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar-ly.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar-ma.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar-ma.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar-ps.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar-ps.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar-sa.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar-sa.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar-tn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar-tn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ar.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ar.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\az.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\az.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\be.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\be.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\bg.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\bg.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\bm.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\bm.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\bn-bd.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\bn-bd.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\bn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\bn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\bo.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\bo.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\br.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\br.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\bs.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\bs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ca.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ca.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\cs.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\cs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\cv.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\cv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\cy.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\cy.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\da.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\da.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\de-at.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\de-at.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\de-ch.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\de-ch.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\de.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\de.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\dv.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\dv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\el.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\el.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-au.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-au.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-ca.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-ca.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-gb.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-gb.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-ie.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-ie.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-il.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-il.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-in.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-in.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-nz.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-nz.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\en-sg.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\en-sg.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\eo.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\eo.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\es-do.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\es-do.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\es-mx.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\es-mx.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\es-us.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\es-us.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\es.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\es.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\et.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\et.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\eu.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\eu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fa.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fa.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fi.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fil.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fil.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fo.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fo.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fr-ca.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fr-ca.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fr-ch.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fr-ch.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fr.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\fy.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\fy.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ga.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ga.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\gd.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\gd.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\gl.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\gl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\gom-deva.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\gom-deva.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\gom-latn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\gom-latn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\gu.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\gu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\he.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\he.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\hi.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\hi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\hr.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\hr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\hu.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\hu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\hy-am.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\hy-am.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\id.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\id.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\is.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\is.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\it-ch.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\it-ch.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\it.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\it.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ja.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ja.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\jv.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\jv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ka.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ka.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\kk.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\kk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\km.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\km.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\kn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\kn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ko.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ko.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ku-kmr.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ku-kmr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ku.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ku.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ky.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ky.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\lb.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\lb.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\lo.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\lo.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\lt.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\lt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\lv.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\lv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\me.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\me.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\mi.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\mi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\mk.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\mk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ml.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ml.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\mn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\mn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\mr.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\mr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ms-my.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ms-my.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ms.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ms.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\mt.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\mt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\my.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\my.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\nb.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\nb.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ne.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ne.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\nl-be.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\nl-be.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\nl.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\nl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\nn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\nn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\oc-lnc.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\oc-lnc.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\pa-in.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\pa-in.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\pl.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\pl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\pt-br.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\pt-br.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\pt.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\pt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ro.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ro.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ru.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ru.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sd.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sd.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\se.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\se.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\si.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\si.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sk.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sl.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sq.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sq.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sr-cyrl.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sr-cyrl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sr.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ss.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ss.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sv.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\sw.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\sw.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ta.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ta.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\te.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\te.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tet.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tet.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tg.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tg.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\th.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\th.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tk.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tl-ph.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tl-ph.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tlh.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tlh.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tr.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tzl.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tzl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tzm-latn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tzm-latn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\tzm.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\tzm.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ug-cn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ug-cn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\uk.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\uk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\ur.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\ur.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\uz-latn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\uz-latn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\uz.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\uz.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\vi.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\vi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\x-pseudo.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\x-pseudo.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\yo.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\yo.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\zh-cn.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\zh-cn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\zh-hk.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\zh-hk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\zh-mo.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\zh-mo.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\locale\\zh-tw.js", "PackagePath": "staticwebassets\\libs\\moment\\locale\\zh-tw.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\moment\\moment.min.js", "PackagePath": "staticwebassets\\libs\\moment\\moment.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\css\\select2.min.css", "PackagePath": "staticwebassets\\libs\\select2\\css\\select2.min.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\af.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\af.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ar.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ar.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\az.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\az.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\bg.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\bg.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\bn.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\bn.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\bs.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\bs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ca.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ca.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\cs.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\cs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\da.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\da.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\de.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\de.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\dsb.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\dsb.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\el.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\el.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\en.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\en.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\es.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\es.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\et.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\et.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\eu.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\eu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\fa.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\fa.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\fi.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\fi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\fr.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\fr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\gl.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\gl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\he.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\he.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\hi.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\hi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\hr.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\hr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\hsb.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\hsb.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\hu.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\hu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\hy.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\hy.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\id.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\id.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\is.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\is.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\it.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\it.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ja.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ja.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ka.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ka.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\km.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\km.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ko.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ko.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\lt.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\lt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\lv.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\lv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\mk.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\mk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ms.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ms.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\nb.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\nb.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ne.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ne.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\nl.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\nl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\pl.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\pl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ps.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ps.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\pt-BR.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\pt-BR.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\pt.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\pt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ro.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ro.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\ru.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\ru.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\sk.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\sk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\sl.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\sl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\sq.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\sq.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\sr-Cyrl.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\sr-Cyrl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\sr.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\sr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\sv.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\sv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\th.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\th.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\tk.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\tk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\tr.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\tr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\uk.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\uk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\vi.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\vi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\zh-CN.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\zh-CN.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\i18n\\zh-TW.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\i18n\\zh-TW.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\select2.full.min.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\select2.full.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\select2\\js\\select2.min.js", "PackagePath": "staticwebassets\\libs\\select2\\js\\select2.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.all.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.all.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.all.min.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.all.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.css", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.esm.all.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.esm.all.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.esm.all.min.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.esm.all.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.esm.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.esm.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.esm.min.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.esm.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.min.css", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.min.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\sweetalert2\\sweetalert2.min.js", "PackagePath": "staticwebassets\\libs\\sweetalert2\\sweetalert2.min.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\jquery.timeago.js", "PackagePath": "staticwebassets\\libs\\timeago\\jquery.timeago.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\README.md", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\README.md"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.af.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.af.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.am.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.am.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ar.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ar.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.az-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.az-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.az.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.az.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.be.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.be.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.bg.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.bg.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.bs.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.bs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ca.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ca.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.cs.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.cs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.cy.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.cy.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.da.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.da.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.de-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.de-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.de.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.de.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.dv.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.dv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.el.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.el.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.en-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.en-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.en.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.en.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.es-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.es-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.es.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.es.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.et.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.et.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.eu.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.eu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.fa-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.fa-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.fa.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.fa.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.fi.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.fi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.fr-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.fr-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.fr.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.fr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.gl.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.gl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.he.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.he.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.hr.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.hr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.hu.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.hu.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.hy.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.hy.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.id.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.id.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.is.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.is.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.it-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.it-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.it.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.it.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ja.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ja.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.jv.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.jv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ko.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ko.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ky.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ky.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.lt.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.lt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.lv.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.lv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.mk.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.mk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.nl.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.nl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.no.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.no.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.pl.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.pl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.pt-br-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.pt-br-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.pt-br.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.pt-br.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.pt-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.pt-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.pt.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.pt.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ro.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ro.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.rs.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.rs.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ru.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ru.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.rw.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.rw.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.si.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.si.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.sk.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.sk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.sl.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.sl.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.sq.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.sq.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.sr.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.sr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.sv.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.sv.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.th.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.th.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.tr-short.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.tr-short.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.tr.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.tr.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.uk.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.uk.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.ur.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.ur.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.uz.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.uz.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.vi.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.vi.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.zh-CN.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.zh-CN.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\timeago\\locales\\jquery.timeago.zh-TW.js", "PackagePath": "staticwebassets\\libs\\timeago\\locales\\jquery.timeago.zh-TW.js"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\toastr\\toastr.css", "PackagePath": "staticwebassets\\libs\\toastr\\toastr.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\toastr\\toastr.js.map", "PackagePath": "staticwebassets\\libs\\toastr\\toastr.js.map"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\toastr\\toastr.min.css", "PackagePath": "staticwebassets\\libs\\toastr\\toastr.min.css"}, {"Id": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\wwwroot\\libs\\toastr\\toastr.min.js", "PackagePath": "staticwebassets\\libs\\toastr\\toastr.min.js"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.AbpTools.AbpHelper.Gui.Blazor.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.AbpTools.AbpHelper.Gui.Blazor.props", "PackagePath": "build\\AbpTools.AbpHelper.Gui.Blazor.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.AbpTools.AbpHelper.Gui.Blazor.props", "PackagePath": "buildMultiTargeting\\AbpTools.AbpHelper.Gui.Blazor.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.AbpTools.AbpHelper.Gui.Blazor.props", "PackagePath": "buildTransitive\\AbpTools.AbpHelper.Gui.Blazor.props"}], "ElementsToRemove": []}