is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = EasyAbp.AbpHelper.Gui.Blazor
build_property.RootNamespace = EasyAbp.AbpHelper.Gui.Blazor
build_property.ProjectDir = D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\dotnet\src\EasyAbp.AbpHelper.Gui.Blazor\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\dotnet\src\EasyAbp.AbpHelper.Gui.Blazor
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/MyApp.razor]
build_metadata.AdditionalFiles.TargetPath = TXlBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Add/AddModule.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcQWRkXEFkZE1vZHVsZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Add/AddPackage.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcQWRkXEFkZFBhY2thZ2UucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Bundle/Bundle.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcQnVuZGxlXEJ1bmRsZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Clean/Clean.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcQ2xlYW5cQ2xlYW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/GetSource/GetSource.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcR2V0U291cmNlXEdldFNvdXJjZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/InstallLibs/InstallLibs.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcSW5zdGFsbExpYnNcSW5zdGFsbExpYnMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Login/Login.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcTG9naW5cTG9naW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Login/Logout.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcTG9naW5cTG9nb3V0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/New/CreateApp.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcTmV3XENyZWF0ZUFwcC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/New/CreateAppNoLayers.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcTmV3XENyZWF0ZUFwcE5vTGF5ZXJzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/New/CreateConsole.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcTmV3XENyZWF0ZUNvbnNvbGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/New/CreateMaui.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcTmV3XENyZWF0ZU1hdWkucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/New/CreateModule.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcTmV3XENyZWF0ZU1vZHVsZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Proxy/AngularProxy.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcUHJveHlcQW5ndWxhclByb3h5LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Proxy/CSharpProxy.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcUHJveHlcQ1NoYXJwUHJveHkucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Proxy/JavaScriptProxy.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcUHJveHlcSmF2YVNjcmlwdFByb3h5LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Switch/SwitchToLocal.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcU3dpdGNoXFN3aXRjaFRvTG9jYWwucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Switch/SwitchToNightly.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcU3dpdGNoXFN3aXRjaFRvTmlnaHRseS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Switch/SwitchToPreRc.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcU3dpdGNoXFN3aXRjaFRvUHJlUmMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Switch/SwitchToPreview.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcU3dpdGNoXFN3aXRjaFRvUHJldmlldy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Switch/SwitchToStable.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcU3dpdGNoXFN3aXRjaFRvU3RhYmxlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Translate/ApplyChanges.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcVHJhbnNsYXRlXEFwcGx5Q2hhbmdlcy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Translate/CreateTranslationFile.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcVHJhbnNsYXRlXENyZWF0ZVRyYW5zbGF0aW9uRmlsZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Components/Update/Update.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXENvbXBvbmVudHNcVXBkYXRlXFVwZGF0ZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/AbpCli/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQWJwQ2xpXEluZGV4LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Components/AppService/GenerateAppServiceClass.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cQ29tcG9uZW50c1xBcHBTZXJ2aWNlXEdlbmVyYXRlQXBwU2VydmljZUNsYXNzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Components/AppService/GenerateAppServiceMethods.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cQ29tcG9uZW50c1xBcHBTZXJ2aWNlXEdlbmVyYXRlQXBwU2VydmljZU1ldGhvZHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Components/Controller/GenerateController.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cQ29tcG9uZW50c1xDb250cm9sbGVyXEdlbmVyYXRlQ29udHJvbGxlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Components/Crud/GenerateCrudCode.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cQ29tcG9uZW50c1xDcnVkXEdlbmVyYXRlQ3J1ZENvZGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Components/Localization/GenerateLocalizationItems.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cQ29tcG9uZW50c1xMb2NhbGl6YXRpb25cR2VuZXJhdGVMb2NhbGl6YXRpb25JdGVtcy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Components/Migration/AddEfMigration.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cQ29tcG9uZW50c1xNaWdyYXRpb25cQWRkRWZNaWdyYXRpb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Components/Migration/RemoveEfMigration.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cQ29tcG9uZW50c1xNaWdyYXRpb25cUmVtb3ZlRWZNaWdyYXRpb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/CodeGeneration/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ29kZUdlbmVyYXRpb25cSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/LogService/Components/Logs.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTG9nU2VydmljZVxDb21wb25lbnRzXExvZ3MucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/ModuleManagement/Components/InstallationActuatorModal.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTW9kdWxlTWFuYWdlbWVudFxDb21wb25lbnRzXEluc3RhbGxhdGlvbkFjdHVhdG9yTW9kYWwucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/ModuleManagement/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTW9kdWxlTWFuYWdlbWVudFxJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/Solutions/Components/SolutionSwitch.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU29sdXRpb25zXENvbXBvbmVudHNcU29sdXRpb25Td2l0Y2gucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/Solutions/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU29sdXRpb25zXEluZGV4LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-du3228dc7m

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/ModuleManagement/Components/Explorer.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTW9kdWxlTWFuYWdlbWVudFxDb21wb25lbnRzXEV4cGxvcmVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = b-ouzqn4c1ns

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/_Host.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX0hvc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Sisprec-AbpHelper/SisprecAbpHelper.GUI/dotnet/src/EasyAbp.AbpHelper.Gui.Blazor/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 
