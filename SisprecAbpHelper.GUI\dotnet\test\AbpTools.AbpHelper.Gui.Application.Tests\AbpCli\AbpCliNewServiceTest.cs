﻿using System.IO;
using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.New;
using AbpTools.AbpHelper.Gui.AbpCli.New.Dtos;
using Microsoft.Extensions.DependencyInjection;
using Shouldly;
using Xunit;

namespace AbpTools.AbpHelper.Gui.AbpCli
{
    public class AbpCliNewServiceTest : GuiApplicationTestBase
    {
        protected const string SolutionName = "CreateAppTest";

        [Fact]
        public async Task Should_Create_App()
        {
            var service = ServiceProvider.GetRequiredService<IAbpCliNewAppService>();

            await service.CreateAppAsync(new AbpNewAppInput(
                SolutionName,
                GuiTestConsts.Folder,
                null,
                false,
                null,
                true,
                null,
                Database.SqlServer,
                null,
                false,
                true,
                false,
                false,
                AppUiFramework.Mvc,
                false,
                false,
                false,
                AppMobileApplicationFramework.None,
                AppDatabaseProvider.Ef,
                AbpThemes.LeptonxLite));

            File.Exists(Path.Combine(Path.Combine(GuiTestConsts.Folder, SolutionName), $"{SolutionName}.sln"))
                .ShouldBeTrue();
        }
    }
}