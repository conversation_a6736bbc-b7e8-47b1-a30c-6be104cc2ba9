﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>AbpTools.AbpHelper.Core</id>
    <version>1.0.0</version>
    <authors>AbpTools</authors>
    <description>AbpTools is a package tool to help you with developing Abp vNext applications.</description>
    <copyright>AbpTools</copyright>
    <repository type="git" commit="8524021b39784202d10d09603e59c8cf3f3acac7" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Bogus" version="35.5.1" exclude="Build,Analyzers" />
        <dependency id="Elsa" version="********" exclude="Build,Analyzers" />
        <dependency id="Humanizer.Core" version="2.14.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.CodeAnalysis.CSharp" version="4.5.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.FileProviders.Embedded" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="NuGet.Protocol" version="6.2.2" exclude="Build,Analyzers" />
        <dependency id="Scriban" version="5.10.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Extensions.Logging" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Sinks.Console" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="Serilog.Sinks.File" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.CommandLine" version="2.0.0-beta1.20371.2" exclude="Build,Analyzers" />
        <dependency id="Volo.Abp.Autofac" version="8.1.1" exclude="Build,Analyzers" />
        <dependency id="Volo.Abp.Core" version="8.1.1" exclude="Build,Analyzers" />
        <dependency id="Volo.Abp.Http" version="8.1.1" exclude="Build,Analyzers" />
        <dependency id="Volo.Abp.VirtualFileSystem" version="8.1.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\AbpHelper.CLI\src\AbpHelper.Core\bin\Debug\net8.0\AbpTools.AbpHelper.Core.pdb" target="lib\net8.0\AbpTools.AbpHelper.Core.pdb" />
    <file src="D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\AbpHelper.CLI\src\AbpHelper.Core\bin\Debug\net8.0\AbpTools.AbpHelper.Core.dll" target="lib\net8.0\AbpTools.AbpHelper.Core.dll" />
  </files>
</package>