<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>
    <ItemGroup>
        <None Remove="appsettings.json" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="appsettings.json">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Volo.Abp.Http.Client.IdentityModel"
                          Version="$(AbpVersion)" />
        <ProjectReference Include="..\..\src\AbpTools.AbpHelper.Gui.HttpApi.Client\AbpTools.AbpHelper.Gui.HttpApi.Client.csproj" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Hosting"
                          Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Http.Polly"
                          Version="8.0.10" />
    </ItemGroup>
</Project>