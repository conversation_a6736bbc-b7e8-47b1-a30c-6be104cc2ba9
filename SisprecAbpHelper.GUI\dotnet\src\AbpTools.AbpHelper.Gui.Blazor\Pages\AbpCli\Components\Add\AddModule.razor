﻿@using Volo.Abp.AspNetCore.Components.Web
@using AbpTools.AbpHelper.Gui.Localization
@using AbpTools.AbpHelper.Gui.AbpCli.Add.Dtos;
@using AbpTools.AbpHelper.Gui.Blazor.Pages.Shared;
@inherits ExecutableComponentBaseWithDirectory<AbpAddModuleInput>
@inject AbpBlazorMessageLocalizerHelper<GuiResource> Lh

<Card Class="mb-4">
    <CardBody>
        <Alert Color="Color.Info"  Visible>
            <AlertDescription>
                <Icon Name="IconName.QuestionCircle" /> @L["Info:FunctionIsBasedOnAbpCliPart1"]
            </AlertDescription>
            <AlertMessage>
                <Icon Name="IconName.ExternalLinkSquareAlt"/>
                <Link To="https://docs.abp.io/en/abp/latest/CLI#add-module" Target="Target.Blank" Style="text-decoration: none">
                    @L["Info:FunctionIsBasedOnAbpCliPart2_Document"]
                </Link>
            </AlertMessage>
            <AlertDescription>
                @L["Info:FunctionIsBasedOnAbpCliPart3"]
            </AlertDescription>
        </Alert>
        <Form>
            <Validations @ref="ValidationsRef" Model="Input" ValidateOnLoad="false">
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["Directory"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Directory">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_Add:ModuleName"]</FieldLabel>
                        <TextEdit @bind-Text="Input.ModuleName">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_Add:Solution"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Solution" Autofocus="true">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_Add:StartupProject"]</FieldLabel>
                        <TextEdit @bind-Text="Input.StartupProject" Autofocus="true">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipDbMigrations">@L["AbpCli_Add:SkipDbMigrations"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.New">@L["AbpCli_Add:New"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.WithSourceCode">@L["AbpCli_Add:WithSourceCode"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.AddToSolutionFile">@L["AbpCli_Add:AddToSolutionFile"]</Check>
                </Field>

                <SubmitButton Block="true" Clicked="@ExecuteAsync" SaveResourceKey="Button:Execute" />
            </Validations>
        </Form>
    </CardBody>
</Card>