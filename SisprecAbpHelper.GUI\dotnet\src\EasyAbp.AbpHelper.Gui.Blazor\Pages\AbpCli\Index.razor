﻿@page "/AbpCli"
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.New
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Update
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Clean
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Add
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.GetSource
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Proxy
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Switch
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Translate
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Login
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Bundle
@using AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.InstallLibs
@inherits GuiComponentBase

<Tabs Pills="true" SelectedTab="AbpNew" TabPosition="TabPosition.Start">
    <Items>
        <Tab Name="AbpNew">abp new</Tab>
        <Tab Name="AbpUpdate">abp update</Tab>
        <Tab Name="AbpClean">abp clean</Tab>
        <Tab Name="AbpAdd">abp add</Tab>
        <Tab Name="AbpGetSource">abp get-source</Tab>
        <Tab Name="AbpProxy">abp proxy</Tab>
        <Tab Name="AbpSwitch">abp switch</Tab>
        <Tab Name="AbpTranslate">abp translate</Tab>
        <Tab Name="AbpLogin">abp login</Tab>
        <Tab Name="AbpBundle">abp bundle</Tab>
        <Tab Name="AbpInstallLibs">abp install-libs</Tab>
    </Items>
    <Content>
        <TabPanel Name="AbpNew">
            <Tabs SelectedTab="CreateApp" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="CreateApp">@L["AbpCli_New:CreateApp"]</Tab>
                    <Tab Name="CreateAppNoLayers">@L["AbpCli_New:CreateAppNoLayers"]</Tab>
                    <Tab Name="CreateModule">@L["AbpCli_New:CreateModule"]</Tab>
                    <Tab Name="CreateConsole">@L["AbpCli_New:CreateConsole"]</Tab>
                    <Tab Name="CreateMaui">@L["AbpCli_New:CreateMaui"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="CreateApp">
                        <CreateApp></CreateApp>
                    </TabPanel>
                    <TabPanel Name="CreateAppNoLayers">
                        <CreateAppNoLayers></CreateAppNoLayers>
                    </TabPanel>
                    <TabPanel Name="CreateModule">
                        <CreateModule></CreateModule>
                    </TabPanel>
                    <TabPanel Name="CreateConsole">
                        <CreateConsole></CreateConsole>
                    </TabPanel>
                    <TabPanel Name="CreateMaui">
                        <CreateMaui></CreateMaui>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpUpdate">
            <Tabs SelectedTab="Update" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="Update">@L["AbpCli_Update"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="Update">
                        <Update></Update>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpClean">
            <Tabs SelectedTab="Clean" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="Clean">@L["AbpCli_Clean"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="Clean">
                        <Clean></Clean>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpAdd">
            <Tabs SelectedTab="AddPackage" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="AddPackage">@L["AbpCli_Add:AddPackage"]</Tab>
                    <Tab Name="AddModule">@L["AbpCli_Add:AddModule"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="AddPackage">
                        <AddPackage></AddPackage>
                    </TabPanel>
                    <TabPanel Name="AddModule">
                        <AddModule></AddModule>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpGetSource">
            <Tabs SelectedTab="GetSource" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="GetSource">@L["AbpCli_GetSource"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="GetSource">
                        <GetSource></GetSource>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpProxy">
            <Tabs SelectedTab="CSharpProxy" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="CSharpProxy">@L["AbpCli_Proxy:CSharpProxy"]</Tab>
                    <Tab Name="AngularProxy">@L["AbpCli_Proxy:AngularProxy"]</Tab>
                    <Tab Name="JavaScriptProxy">@L["AbpCli_Proxy:JavaScriptProxy"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="CSharpProxy">
                        <CSharpProxy></CSharpProxy>
                    </TabPanel>
                    <TabPanel Name="AngularProxy">
                        <AngularProxy></AngularProxy>
                    </TabPanel>
                    <TabPanel Name="JavaScriptProxy">
                        <JavaScriptProxy></JavaScriptProxy>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpSwitch">
            <Tabs SelectedTab="SwitchToPreview" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="SwitchToPreview">@L["AbpCli_Switch:SwitchToPreview"]</Tab>
                    <Tab Name="SwitchToNightly">@L["AbpCli_Switch:SwitchToNightly"]</Tab>
                    <Tab Name="SwitchToPreRc">@L["AbpCli_Switch:SwitchToPreRc"]</Tab>
                    <Tab Name="SwitchToStable">@L["AbpCli_Switch:SwitchToStable"]</Tab>
                    <Tab Name="SwitchToLocal">@L["AbpCli_Switch:SwitchToLocal"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="SwitchToPreview">
                        <SwitchToPreview></SwitchToPreview>
                    </TabPanel>
                    <TabPanel Name="SwitchToNightly">
                        <SwitchToNightly></SwitchToNightly>
                    </TabPanel>
                    <TabPanel Name="SwitchToPreRc">
                        <SwitchToPreRc></SwitchToPreRc>
                    </TabPanel>
                    <TabPanel Name="SwitchToStable">
                        <SwitchToStable></SwitchToStable>
                    </TabPanel>
                    <TabPanel Name="SwitchToLocal">
                        <SwitchToLocal></SwitchToLocal>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpTranslate">
            <Tabs SelectedTab="CreateTranslationFile" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="CreateTranslationFile">@L["AbpCli_Translate:CreateTranslationFile"]</Tab>
                    <Tab Name="ApplyChanges">@L["AbpCli_Translate:ApplyChanges"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="CreateTranslationFile">
                        <CreateTranslationFile></CreateTranslationFile>
                    </TabPanel>
                    <TabPanel Name="ApplyChanges">
                        <ApplyChanges></ApplyChanges>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpLogin">
            <Tabs SelectedTab="Login" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="Login">@L["AbpCli_Login:Login"]</Tab>
                    <Tab Name="Logout">@L["AbpCli_Login:Logout"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="Login">
                        <Login></Login>
                    </TabPanel>
                    <TabPanel Name="Logout">
                        <Logout></Logout>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpBundle">
            <Tabs SelectedTab="Bundle" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="Bundle">@L["AbpCli_Bundle"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="Bundle">
                        <Bundle></Bundle>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AbpInstallLibs">
            <Tabs SelectedTab="InstallLibs" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="InstallLibs">@L["AbpCli_InstallLibs"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="InstallLibs">
                        <InstallLibs></InstallLibs>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
    </Content>
</Tabs>