﻿@page "/CodeGeneration"
@using AbpTools.AbpHelper.Gui.Blazor.Pages.CodeGeneration.Components.Crud
@using AbpTools.AbpHelper.Gui.Blazor.Pages.CodeGeneration.Components.AppService
@using AbpTools.AbpHelper.Gui.Blazor.Pages.CodeGeneration.Components.Controller
@using AbpTools.AbpHelper.Gui.Blazor.Pages.CodeGeneration.Components.Localization
@using AbpTools.AbpHelper.Gui.Blazor.Pages.CodeGeneration.Components.Migration
@inherits GuiComponentBase

<Tabs Pills="true" SelectedTab="Crud" TabPosition="TabPosition.Start">
    <Items>
        <Tab Name="Crud">@L["CodeGeneration_Crud"]</Tab>
        <Tab Name="AppService">@L["CodeGeneration_AppService"]</Tab>
        <Tab Name="Controller">@L["CodeGeneration_Controller"]</Tab>
        <Tab Name="Localization">@L["CodeGeneration_Localization"]</Tab>
        <Tab Name="Migration">@L["CodeGeneration_Migration"]</Tab>
    </Items>
    <Content>
        <TabPanel Name="Crud">
            <Tabs SelectedTab="GenerateCrudCode" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="GenerateCrudCode">@L["CodeGeneration_Crud:GenerateCrudCode"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="GenerateCrudCode">
                        <GenerateCrudCode></GenerateCrudCode>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="AppService">
            <Tabs SelectedTab="GenerateClass" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="GenerateClass">@L["CodeGeneration_AppService:GenerateClass"]</Tab>
                    <Tab Name="GenerateMethods">@L["CodeGeneration_AppService:GenerateMethods"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="GenerateClass">
                        <GenerateAppServiceClass></GenerateAppServiceClass>
                    </TabPanel>
                    <TabPanel Name="GenerateMethods">
                        <GenerateAppServiceMethods></GenerateAppServiceMethods>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="Controller">
            <Tabs SelectedTab="GenerateController" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="GenerateController">@L["CodeGeneration_Controller:GenerateController"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="GenerateController">
                        <GenerateController></GenerateController>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="Localization">
            <Tabs SelectedTab="GenerateLocalizationItems" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="GenerateLocalizationItems">@L["CodeGeneration_Localization:GenerateLocalizationItems"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="GenerateLocalizationItems">
                        <GenerateLocalizationItems></GenerateLocalizationItems>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
        <TabPanel Name="Migration">
            <Tabs SelectedTab="AddEfMigration" TabPosition="TabPosition.Top">
                <Items>
                    <Tab Name="AddEfMigration">@L["CodeGeneration_Migration:AddEfMigration"]</Tab>
                    <Tab Name="RemoveEfMigration">@L["CodeGeneration_Migration:RemoveEfMigration"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="AddEfMigration">
                        <AddEfMigration></AddEfMigration>
                    </TabPanel>
                    <TabPanel Name="RemoveEfMigration">
                        <RemoveEfMigration></RemoveEfMigration>
                    </TabPanel>
                </Content>
            </Tabs>
        </TabPanel>
    </Content>
</Tabs>