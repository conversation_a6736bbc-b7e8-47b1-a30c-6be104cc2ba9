﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.Blazor.Services;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.Shared
{
    public abstract class ExecutableComponentBaseWithDirectory<TInput> : ExecutableComponentBaseWithCurrentSolution where TInput : InputDtoWithDirectory, new()
    {
        [Inject]
        protected ICurrentSolution CurrentSolution { get; set; }
        
        protected TInput Input { get; set; } = new();
        
        protected override Task OnInitializedAsync()
        {
            SetDirectoryToCurrentSolutionPath();
            
            return base.OnInitializedAsync();
        }

        protected override Task OnCurrentSolutionChangedAsync()
        {
            SetDirectoryToCurrentSolutionPath();
            
            return Task.CompletedTask;
        }
        
        protected virtual void SetDirectoryToCurrentSolutionPath()
        {
            Input.Directory = CurrentSolution.Value?.DirectoryPath ?? string.Empty;
        }
    }
}