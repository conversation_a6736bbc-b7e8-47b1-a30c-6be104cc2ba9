2025-06-02 04:35:26.364 -03:00 [ERR] Unhandled exception in circuit 'Oe3nCBB-sfgsXaAIkvo7kaPWtYURUKyp-Zrsy53Tg30'.
System.AggregateException: One or more errors occurred. (TypeError: Cannot read properties of null (reading 'removeChild'))
 ---> System.InvalidOperationException: TypeError: Cannot read properties of null (reading 'removeChild')
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-06-02 04:35:26.368 -03:00 [ERR] Unhandled exception in circuit 'Oe3nCBB-sfgsXaAIkvo7kaPWtYURUKyp-Zrsy53Tg30'.
System.AggregateException: One or more errors occurred. (Error: No element is currently associated with component 36)
 ---> System.InvalidOperationException: Error: No element is currently associated with component 36
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-06-02 04:35:31.741 -03:00 [ERR] Unhandled exception in circuit 'bnUnu8-GoIfbNExjLWZCOvB7Bd_SPM989iOTg49nJHs'.
System.AggregateException: One or more errors occurred. (Error: There is no browser renderer with ID 1.)
 ---> System.InvalidOperationException: Error: There is no browser renderer with ID 1.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-06-02 04:35:33.236 -03:00 [ERR] Unhandled exception in circuit 'BzHhYw3MGbT5tG1KfJoq6HcmQPuwcWA9Hs1ZsyN5zMU'.
System.AggregateException: One or more errors occurred. (Error: There is no browser renderer with ID 1.)
 ---> System.InvalidOperationException: Error: There is no browser renderer with ID 1.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-06-02 04:35:33.238 -03:00 [ERR] Unhandled exception in circuit 'BzHhYw3MGbT5tG1KfJoq6HcmQPuwcWA9Hs1ZsyN5zMU'.
System.AggregateException: One or more errors occurred. (Error: There is no browser renderer with ID 1.)
 ---> System.InvalidOperationException: Error: There is no browser renderer with ID 1.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
