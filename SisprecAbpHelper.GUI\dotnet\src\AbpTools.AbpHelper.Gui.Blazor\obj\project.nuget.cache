{"version": 2, "dgSpecHash": "GB3p8gMh0yE=", "success": true, "projectFilePath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\AbpTools.AbpHelper.Gui.Blazor\\AbpTools.AbpHelper.Gui.Blazor.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.abstractions\\8.1.0\\asp.versioning.abstractions.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.http\\8.1.0\\asp.versioning.http.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.mvc\\8.1.0\\asp.versioning.mvc.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.mvc.apiexplorer\\8.1.0\\asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asynckeyedlock\\6.3.4\\asynckeyedlock.6.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac\\8.0.0\\autofac.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extensions.dependencyinjection\\9.0.0\\autofac.extensions.dependencyinjection.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extras.dynamicproxy\\7.1.0\\autofac.extras.dynamicproxy.7.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.6.2\\blazorise.1.6.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.6.2\\blazorise.bootstrap5.1.6.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.components\\1.5.2\\blazorise.components.1.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.datagrid\\1.5.2\\blazorise.datagrid.1.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.icons.fontawesome\\1.6.2\\blazorise.icons.fontawesome.1.6.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.licensing\\1.2.0\\blazorise.licensing.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.snackbar\\1.5.2\\blazorise.snackbar.1.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\blazorx-analytics\\1.0.0\\blazorx-analytics.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bogus\\35.5.1\\bogus.35.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core\\5.1.1\\castle.core.5.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core.asyncinterceptor\\2.1.0\\castle.core.asyncinterceptor.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\deepl.net\\1.8.0\\deepl.net.1.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devicedetector.net\\6.1.4\\devicedetector.net.6.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa\\********\\elsa.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.abstractions\\********\\elsa.abstractions.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.activities.console\\********\\elsa.activities.console.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.activities.controlflow\\********\\elsa.activities.controlflow.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.activities.email\\********\\elsa.activities.email.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.activities.http\\********\\elsa.activities.http.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.activities.timers\\********\\elsa.activities.timers.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.activities.usertask\\********\\elsa.activities.usertask.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.activities.workflows\\********\\elsa.activities.workflows.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.automapper.extensions\\********\\elsa.automapper.extensions.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.core\\********\\elsa.core.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.scripting.javascript\\********\\elsa.scripting.javascript.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.scripting.liquid\\********\\elsa.scripting.liquid.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elsa.scrutor\\********\\elsa.scrutor.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\esprima\\1.0.1246\\esprima.1.0.1246.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluid.core\\1.0.0-beta-9605\\fluid.core.1.0.0-beta-9605.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identitymodel\\6.2.0\\identitymodel.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\irony.core\\1.0.7\\irony.core.1.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2023.3.0\\jetbrains.annotations.2023.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jint\\3.0.0-beta-1629\\jint.3.0.0-beta-1629.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libgit2sharp\\0.28.0\\libgit2sharp.0.28.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libgit2sharp.nativebinaries\\2.0.320\\libgit2sharp.nativebinaries.2.0.320.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\litedb\\5.0.17\\litedb.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mailkit\\2.4.1\\mailkit.2.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\8.0.0\\mediatr.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.extensions.microsoft.dependencyinjection\\8.0.0\\mediatr.extensions.microsoft.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.core\\2.2.0\\microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.openidconnect\\8.0.4\\microsoft.aspnetcore.authentication.openidconnect.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\8.0.10\\microsoft.aspnetcore.authorization.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization.policy\\2.2.0\\microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\8.0.10\\microsoft.aspnetcore.components.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\8.0.10\\microsoft.aspnetcore.components.analyzers.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.authorization\\8.0.4\\microsoft.aspnetcore.components.authorization.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\8.0.10\\microsoft.aspnetcore.components.forms.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\8.0.10\\microsoft.aspnetcore.components.web.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\3.1.1\\microsoft.aspnetcore.cryptography.internal.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\3.1.1\\microsoft.aspnetcore.dataprotection.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\3.1.1\\microsoft.aspnetcore.dataprotection.abstractions.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http\\2.2.2\\microsoft.aspnetcore.http.2.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\8.0.10\\microsoft.aspnetcore.metadata.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.abstractions\\2.2.0\\microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.core\\2.2.5\\microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.extensions\\6.0.0\\microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.runtimecompilation\\8.0.4\\microsoft.aspnetcore.mvc.razor.runtimecompilation.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\6.0.0\\microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.responsecaching.abstractions\\2.2.0\\microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing\\2.2.0\\microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.routing.abstractions\\2.2.0\\microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.3.3\\microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.5.0\\microsoft.codeanalysis.common.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.5.0\\microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.razor\\6.0.0\\microsoft.codeanalysis.razor.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.0\\microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\microsoft.extensions.configuration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.0\\microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\8.0.0\\microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\8.0.0\\microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.0\\microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.0\\microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\8.0.0\\microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.1\\microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\8.0.1\\microsoft.extensions.dependencymodel.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\8.0.0\\microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.0\\microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.0\\microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\8.0.0\\microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\9.0.0\\microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.0\\microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\8.0.0\\microsoft.extensions.http.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.polly\\5.0.1\\microsoft.extensions.http.polly.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\8.0.0\\microsoft.extensions.localization.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\8.0.0\\microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.2\\microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\3.1.1\\microsoft.extensions.logging.configuration.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\3.1.1\\microsoft.extensions.logging.console.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\3.1.1\\microsoft.extensions.logging.debug.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\2.2.0\\microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.2\\microsoft.extensions.options.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.0\\microsoft.extensions.primitives.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.1.2\\microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.1.2\\microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.1.2\\microsoft.identitymodel.logging.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.1.2\\microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.1.2\\microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.1.2\\microsoft.identitymodel.tokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\8.0.10\\microsoft.jsinterop.8.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.2.3\\microsoft.openapi.1.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mimekit\\2.4.1\\mimekit.2.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ncrontab\\3.3.1\\ncrontab.3.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.context\\5.1.2\\nito.asyncex.context.5.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.tasks\\5.1.2\\nito.asyncex.tasks.5.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.disposables\\2.2.1\\nito.disposables.2.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nodatime\\3.0.0-beta01\\nodatime.3.0.0-beta01.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nodatime.serialization.jsonnet\\2.2.0\\nodatime.serialization.jsonnet.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.common\\6.2.2\\nuget.common.6.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.configuration\\6.2.2\\nuget.configuration.6.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.frameworks\\6.2.2\\nuget.frameworks.6.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.packaging\\6.2.2\\nuget.packaging.6.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.protocol\\6.2.2\\nuget.protocol.6.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.versioning\\6.7.0\\nuget.versioning.6.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuglify\\1.21.0\\nuglify.1.21.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pipelines.sockets.unofficial\\2.2.8\\pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly\\8.2.0\\polly.8.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.core\\8.2.0\\polly.core.8.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.extensions.http\\3.0.0\\polly.extensions.http.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\portable.bouncycastle\\1.8.5\\portable.bouncycastle.1.8.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.security\\4.3.0\\runtime.native.system.net.security.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\scriban\\5.10.0\\scriban.5.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.0.0\\serilog.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\8.0.2\\serilog.aspnetcore.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\8.0.0\\serilog.extensions.hosting.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\8.0.0\\serilog.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\2.0.0\\serilog.formatting.compact.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\8.0.2\\serilog.settings.configuration.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.async\\2.0.0\\serilog.sinks.async.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\6.0.0\\serilog.sinks.console.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\6.0.0\\serilog.sinks.file.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpziplib\\1.4.2\\sharpziplib.1.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stackexchange.redis\\2.7.4\\stackexchange.redis.2.7.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\6.5.0\\swashbuckle.aspnetcore.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\6.5.0\\swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\6.5.0\\swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\6.5.0\\swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.commandline\\2.0.0-beta1.20371.2\\system.commandline.2.0.0-beta1.20371.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\4.7.0\\system.componentmodel.annotations.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.common\\4.3.0\\system.data.common.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\8.0.0\\system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\6.0.0\\system.diagnostics.eventlog.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\5.0.0\\system.formats.asn1.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.1.2\\system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\8.0.0\\system.io.pipelines.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.3.5\\system.linq.dynamic.core.1.3.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.3.0\\system.linq.queryable.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.nameresolution\\4.3.0\\system.net.nameresolution.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.security\\4.3.2\\system.net.security.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\6.0.1\\system.reflection.metadata.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.claims\\4.3.0\\system.security.claims.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\5.0.0\\system.security.cryptography.pkcs.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.4.0\\system.security.cryptography.protecteddata.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\4.7.0\\system.security.cryptography.xml.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\8.0.0\\system.security.permissions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal\\4.3.0\\system.security.principal.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\8.0.0\\system.text.encoding.codepages.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.4\\system.text.json.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.2\\system.threading.tasks.extensions.4.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.threadpool\\4.3.0\\system.threading.threadpool.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\8.0.0\\system.windows.extensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\timezoneconverter\\6.1.0\\timezoneconverter.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.account.application.contracts\\8.1.1\\volo.abp.account.application.contracts.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.account.httpapi\\8.1.1\\volo.abp.account.httpapi.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.apiversioning.abstractions\\8.3.4\\volo.abp.apiversioning.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore\\8.3.4\\volo.abp.aspnetcore.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.abstractions\\8.3.4\\volo.abp.aspnetcore.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components\\8.3.4\\volo.abp.aspnetcore.components.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components.server\\8.3.4\\volo.abp.aspnetcore.components.server.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components.server.leptonxlitetheme\\3.3.4\\volo.abp.aspnetcore.components.server.leptonxlitetheme.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components.server.theming\\8.3.4\\volo.abp.aspnetcore.components.server.theming.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components.web\\8.3.4\\volo.abp.aspnetcore.components.web.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components.web.leptonxlitetheme\\3.3.4\\volo.abp.aspnetcore.components.web.leptonxlitetheme.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.components.web.theming\\8.3.4\\volo.abp.aspnetcore.components.web.theming.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.multitenancy\\8.1.1\\volo.abp.aspnetcore.multitenancy.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc\\8.3.4\\volo.abp.aspnetcore.mvc.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.contracts\\8.3.4\\volo.abp.aspnetcore.mvc.contracts.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui\\8.3.4\\volo.abp.aspnetcore.mvc.ui.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bootstrap\\8.3.4\\volo.abp.aspnetcore.mvc.ui.bootstrap.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bundling\\8.3.4\\volo.abp.aspnetcore.mvc.ui.bundling.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.bundling.abstractions\\8.3.4\\volo.abp.aspnetcore.mvc.ui.bundling.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.multitenancy\\8.1.1\\volo.abp.aspnetcore.mvc.ui.multitenancy.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.packages\\8.3.4\\volo.abp.aspnetcore.mvc.ui.packages.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.theme.basic\\8.1.1\\volo.abp.aspnetcore.mvc.ui.theme.basic.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.theme.shared\\8.1.1\\volo.abp.aspnetcore.mvc.ui.theme.shared.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.ui.widgets\\8.1.1\\volo.abp.aspnetcore.mvc.ui.widgets.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.serilog\\8.1.1\\volo.abp.aspnetcore.serilog.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.signalr\\8.3.4\\volo.abp.aspnetcore.signalr.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing\\8.3.4\\volo.abp.auditing.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing.contracts\\8.3.4\\volo.abp.auditing.contracts.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization\\8.3.4\\volo.abp.authorization.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization.abstractions\\8.3.4\\volo.abp.authorization.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.autofac\\8.1.1\\volo.abp.autofac.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.automapper\\8.1.1\\volo.abp.automapper.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundworkers\\8.3.4\\volo.abp.backgroundworkers.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.blazoriseui\\8.3.4\\volo.abp.blazoriseui.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching\\8.3.4\\volo.abp.caching.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.castle.core\\8.3.4\\volo.abp.castle.core.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.cli.core\\8.1.1\\volo.abp.cli.core.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.core\\8.3.4\\volo.abp.core.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.data\\8.3.4\\volo.abp.data.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application\\8.3.4\\volo.abp.ddd.application.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application.contracts\\8.3.4\\volo.abp.ddd.application.contracts.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain\\8.3.4\\volo.abp.ddd.domain.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain.shared\\8.3.4\\volo.abp.ddd.domain.shared.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.distributedlocking.abstractions\\8.3.4\\volo.abp.distributedlocking.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus\\8.3.4\\volo.abp.eventbus.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus.abstractions\\8.3.4\\volo.abp.eventbus.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.exceptionhandling\\8.3.4\\volo.abp.exceptionhandling.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.features\\8.3.4\\volo.abp.features.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.globalfeatures\\8.3.4\\volo.abp.globalfeatures.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.guids\\8.3.4\\volo.abp.guids.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http\\8.3.4\\volo.abp.http.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http.abstractions\\8.3.4\\volo.abp.http.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http.client\\8.3.4\\volo.abp.http.client.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.application.contracts\\8.1.1\\volo.abp.identity.application.contracts.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.domain.shared\\8.1.1\\volo.abp.identity.domain.shared.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identity.httpapi\\8.1.1\\volo.abp.identity.httpapi.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.identitymodel\\8.1.1\\volo.abp.identitymodel.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json\\8.3.4\\volo.abp.json.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json.abstractions\\8.3.4\\volo.abp.json.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json.systemtextjson\\8.3.4\\volo.abp.json.systemtextjson.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization\\8.3.4\\volo.abp.localization.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization.abstractions\\8.3.4\\volo.abp.localization.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.minify\\8.3.4\\volo.abp.minify.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy\\8.3.4\\volo.abp.multitenancy.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy.abstractions\\8.3.4\\volo.abp.multitenancy.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectextending\\8.3.4\\volo.abp.objectextending.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectmapping\\8.3.4\\volo.abp.objectmapping.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.application.contracts\\8.1.1\\volo.abp.permissionmanagement.application.contracts.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.permissionmanagement.domain.shared\\8.1.1\\volo.abp.permissionmanagement.domain.shared.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.remoteservices\\8.3.4\\volo.abp.remoteservices.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.security\\8.3.4\\volo.abp.security.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.serialization\\8.3.4\\volo.abp.serialization.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.application.contracts\\8.1.1\\volo.abp.settingmanagement.application.contracts.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.blazor\\8.1.1\\volo.abp.settingmanagement.blazor.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.blazor.server\\8.1.1\\volo.abp.settingmanagement.blazor.server.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settingmanagement.domain.shared\\8.1.1\\volo.abp.settingmanagement.domain.shared.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settings\\8.3.4\\volo.abp.settings.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.specifications\\8.3.4\\volo.abp.specifications.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.swashbuckle\\8.1.1\\volo.abp.swashbuckle.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.threading\\8.3.4\\volo.abp.threading.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.timing\\8.3.4\\volo.abp.timing.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui\\8.3.4\\volo.abp.ui.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui.navigation\\8.3.4\\volo.abp.ui.navigation.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.uow\\8.3.4\\volo.abp.uow.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.abstractions\\8.1.1\\volo.abp.users.abstractions.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.users.domain.shared\\8.1.1\\volo.abp.users.domain.shared.8.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation\\8.3.4\\volo.abp.validation.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation.abstractions\\8.3.4\\volo.abp.validation.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.virtualfilesystem\\8.3.4\\volo.abp.virtualfilesystem.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\yamldotnet\\13.1.1\\yamldotnet.13.1.1.nupkg.sha512"], "logs": []}