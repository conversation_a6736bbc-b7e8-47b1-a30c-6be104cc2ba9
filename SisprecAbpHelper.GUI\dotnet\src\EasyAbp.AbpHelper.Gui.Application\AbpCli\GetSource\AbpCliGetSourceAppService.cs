﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.GetSource.Dtos;
using AbpTools.AbpHelper.Gui.Common;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Cli.Commands;

namespace AbpTools.AbpHelper.Gui.AbpCli.GetSource
{
    public class AbpCliGetSourceAppService : AbpCliAppService, IAbpCliGetSourceAppService
    {
        private readonly GetSourceCommand _getSourceCommand;
        private readonly ICurrentDirectoryHelper _currentDirectoryHelper;

        public AbpCliGetSourceAppService(
            GetSourceCommand getSourceCommand,
            ICurrentDirectoryHelper currentDirectoryHelper)
        {
            _getSourceCommand = getSourceCommand;
            _currentDirectoryHelper = currentDirectoryHelper;
        }

        public virtual async Task<ServiceExecutionResult> GetSourceAsync(AbpGetSourceInput input)
        {
            var args = CreateCommandLineArgs(input, "abp get-source", input.ModuleName);

            using (_currentDirectoryHelper.Change(input.Directory))
            {
                await _getSourceCommand.ExecuteAsync(args);
            }

            return new ServiceExecutionResult(true);
        }
    }
}