{"version": 3, "targets": {"net8.0": {"AsyncKeyedLock/6.3.4": {"type": "package", "compile": {"lib/net8.0/AsyncKeyedLock.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"related": ".xml"}}}, "Castle.Core/5.1.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "dependencies": {"Castle.Core": "4.4.0"}, "compile": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"related": ".xml"}}}, "IdentityModel/6.2.0": {"type": "package", "compile": {"lib/net6.0/IdentityModel.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/IdentityModel.dll": {"related": ".pdb;.xml"}}}, "JetBrains.Annotations/2023.3.0": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "Microsoft.AspNetCore.Authorization/8.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.1", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.Logging.Console": "8.0.1", "Microsoft.Extensions.Logging.Debug": "8.0.1", "Microsoft.Extensions.Logging.EventLog": "8.0.1", "Microsoft.Extensions.Logging.EventSource": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Http/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Http.Polly/8.0.10": {"type": "package", "dependencies": {"Microsoft.Extensions.Http": "8.0.1", "Polly": "7.2.4", "Polly.Extensions.Http": "3.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.EventLog": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "dependencies": {"Nito.Disposables": "2.2.1"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}}, "Nito.Disposables/2.2.1": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.7.1"}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}}, "NUglify/1.21.0": {"type": "package", "compile": {"lib/net5.0/NUglify.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/NUglify.dll": {"related": ".xml"}}}, "Polly/7.2.4": {"type": "package", "compile": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}}, "Polly.Extensions.Http/3.0.0": {"type": "package", "dependencies": {"Polly": "7.1.0"}, "compile": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"related": ".xml"}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.EventLog/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "compile": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Linq.Queryable/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Linq.Queryable.dll": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "TimeZoneConverter/6.1.0": {"type": "package", "compile": {"lib/net6.0/TimeZoneConverter.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TimeZoneConverter.dll": {"related": ".xml"}}}, "Volo.Abp.Account.Application.Contracts/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Identity.Application.Contracts": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Account.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Account.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Account.HttpApi.Client/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Account.Application.Contracts": "8.1.1", "Volo.Abp.Http.Client": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Account.HttpApi.Client.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Account.HttpApi.Client.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Auditing.Contracts/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Security": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization.Abstractions/8.1.1": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.0", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.BackgroundWorkers/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Threading": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Caching/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "Volo.Abp.Json": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Serialization": "8.1.1", "Volo.Abp.Threading": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Caching.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Caching.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Castle.Core/8.1.1": {"type": "package", "dependencies": {"Castle.Core": "5.1.1", "Castle.Core.AsyncInterceptor": "2.1.0", "Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Castle.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Castle.Core.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Core/8.1.1": {"type": "package", "dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Data/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.ObjectExtending": "8.1.1", "Volo.Abp.Uow": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application.Contracts/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.1", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.DistributedLocking.Abstractions/8.1.1": {"type": "package", "dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.BackgroundWorkers": "8.1.1", "Volo.Abp.DistributedLocking.Abstractions": "8.1.1", "Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.Guids": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus.Abstractions/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.ObjectExtending": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ExceptionHandling/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Features/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Guids/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Http.Abstractions": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.Minify": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http.Abstractions/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http.Client/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Volo.Abp.Castle.Core": "8.1.1", "Volo.Abp.EventBus": "8.1.1", "Volo.Abp.ExceptionHandling": "8.1.1", "Volo.Abp.Http": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.RemoteServices": "8.1.1", "Volo.Abp.Threading": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Http.Client.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.Client.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http.Client.IdentityModel/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Http.Client": "8.1.1", "Volo.Abp.IdentityModel": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Http.Client.IdentityModel.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.Client.IdentityModel.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Identity.Application.Contracts/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "8.1.1", "Volo.Abp.Identity.Domain.Shared": "8.1.1", "Volo.Abp.PermissionManagement.Application.Contracts": "8.1.1", "Volo.Abp.Users.Abstractions": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Identity.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Identity.Domain.Shared/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Auditing.Contracts": "8.1.1", "Volo.Abp.Features": "8.1.1", "Volo.Abp.Users.Domain.Shared": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Identity.HttpApi.Client/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Http.Client": "8.1.1", "Volo.Abp.Identity.Application.Contracts": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Identity.HttpApi.Client.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Identity.HttpApi.Client.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.IdentityModel/8.1.1": {"type": "package", "dependencies": {"IdentityModel": "6.2.0", "Microsoft.Extensions.Http": "8.0.0", "Volo.Abp.Caching": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Threading": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.IdentityModel.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.IdentityModel.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Json.SystemTextJson": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json.Abstractions/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json.SystemTextJson/8.1.1": {"type": "package", "dependencies": {"System.Text.Json": "8.0.0", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Json.Abstractions": "8.1.1", "Volo.Abp.Timing": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Settings": "8.1.1", "Volo.Abp.Threading": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization.Abstractions/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Minify/8.1.1": {"type": "package", "dependencies": {"NUglify": "1.21.0", "Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Data": "8.1.1", "Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1", "Volo.Abp.Security": "8.1.1", "Volo.Abp.Settings": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy.Abstractions/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Localization": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectExtending/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Validation.Abstractions": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.PermissionManagement.Application.Contracts/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Ddd.Application.Contracts": "8.1.1", "Volo.Abp.PermissionManagement.Domain.Shared": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.PermissionManagement.Domain.Shared/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Volo.Abp.Validation": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.RemoteServices/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.RemoteServices.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.RemoteServices.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Security/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Serialization/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Serialization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Serialization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Settings/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Security": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Threading/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Timing/8.1.1": {"type": "package", "dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.Settings": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Uow/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Users.Abstractions/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.EventBus": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Users.Domain.Shared/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Localization": "8.1.1", "Volo.Abp.Validation.Abstractions": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation.Abstractions/8.1.1": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.VirtualFileSystem/8.1.1": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.1.1"}, "compile": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}}, "EasyAbp.AbpHelper.Gui.Application.Contracts/2.16.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Account.Application.Contracts": "8.1.1", "Volo.Abp.Identity.Application.Contracts": "8.1.1", "Volo.Abp.ObjectExtending": "8.1.1"}, "compile": {"bin/placeholder/EasyAbp.AbpHelper.Gui.Application.Contracts.dll": {}}, "runtime": {"bin/placeholder/EasyAbp.AbpHelper.Gui.Application.Contracts.dll": {}}}, "EasyAbp.AbpHelper.Gui.HttpApi.Client/2.16.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"EasyAbp.AbpHelper.Gui.Application.Contracts": "2.16.0", "Volo.Abp.Account.HttpApi.Client": "8.1.1", "Volo.Abp.Identity.HttpApi.Client": "8.1.1"}, "compile": {"bin/placeholder/EasyAbp.AbpHelper.Gui.HttpApi.Client.dll": {}}, "runtime": {"bin/placeholder/EasyAbp.AbpHelper.Gui.HttpApi.Client.dll": {}}}}}, "libraries": {"AsyncKeyedLock/6.3.4": {"sha512": "+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "type": "package", "path": "asynckeyedlock/6.3.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "asynckeyedlock.6.3.4.nupkg.sha512", "asynckeyedlock.nuspec", "lib/net5.0/AsyncKeyedLock.dll", "lib/net5.0/AsyncKeyedLock.xml", "lib/net8.0/AsyncKeyedLock.dll", "lib/net8.0/AsyncKeyedLock.xml", "lib/netstandard2.0/AsyncKeyedLock.dll", "lib/netstandard2.0/AsyncKeyedLock.xml", "lib/netstandard2.1/AsyncKeyedLock.dll", "lib/netstandard2.1/AsyncKeyedLock.xml", "logo.png"]}, "Castle.Core/5.1.1": {"sha512": "rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "type": "package", "path": "castle.core/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle-logo.png", "castle.core.5.1.1.nupkg.sha512", "castle.core.nuspec", "lib/net462/Castle.Core.dll", "lib/net462/Castle.Core.xml", "lib/net6.0/Castle.Core.dll", "lib/net6.0/Castle.Core.xml", "lib/netstandard2.0/Castle.Core.dll", "lib/netstandard2.0/Castle.Core.xml", "lib/netstandard2.1/Castle.Core.dll", "lib/netstandard2.1/Castle.Core.xml", "readme.txt"]}, "Castle.Core.AsyncInterceptor/2.1.0": {"sha512": "1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "type": "package", "path": "castle.core.asyncinterceptor/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "castle-logo.png", "castle.core.asyncinterceptor.2.1.0.nupkg.sha512", "castle.core.asyncinterceptor.nuspec", "lib/net45/Castle.Core.AsyncInterceptor.dll", "lib/net45/Castle.Core.AsyncInterceptor.xml", "lib/net5.0/Castle.Core.AsyncInterceptor.dll", "lib/net5.0/Castle.Core.AsyncInterceptor.xml", "lib/net6.0/Castle.Core.AsyncInterceptor.dll", "lib/net6.0/Castle.Core.AsyncInterceptor.xml", "lib/netstandard2.0/Castle.Core.AsyncInterceptor.dll", "lib/netstandard2.0/Castle.Core.AsyncInterceptor.xml"]}, "IdentityModel/6.2.0": {"sha512": "4AXZ6Tp+DNwrSSeBziiX/231i8ZpD77A9nEMyc68gLSCWG0kgWsIBeFquYcBebiIPkfB7GEXzCYuuLeR1QZJIQ==", "type": "package", "path": "identitymodel/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.jpg", "identitymodel.6.2.0.nupkg.sha512", "identitymodel.nuspec", "lib/net461/IdentityModel.dll", "lib/net461/IdentityModel.pdb", "lib/net461/IdentityModel.xml", "lib/net472/IdentityModel.dll", "lib/net472/IdentityModel.pdb", "lib/net472/IdentityModel.xml", "lib/net6.0/IdentityModel.dll", "lib/net6.0/IdentityModel.pdb", "lib/net6.0/IdentityModel.xml", "lib/netstandard2.0/IdentityModel.dll", "lib/netstandard2.0/IdentityModel.pdb", "lib/netstandard2.0/IdentityModel.xml"]}, "JetBrains.Annotations/2023.3.0": {"sha512": "PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "type": "package", "path": "jetbrains.annotations/2023.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2023.3.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "Microsoft.AspNetCore.Authorization/8.0.0": {"sha512": "OGIGJMnlWvQgcweHcv1Mq/P24Zx/brUHeEdD05NzqkSXmQSnFomTvVyCuBtCXT4JPfv2m70y1RSocmd9bIbJRg==", "type": "package", "path": "microsoft.aspnetcore.authorization/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net8.0/Microsoft.AspNetCore.Authorization.dll", "lib/net8.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Metadata/8.0.0": {"sha512": "OmuSztiZMitRTYlbMNDkBk3BinSsVcOApSNBAsrw+KYNJh6ALarPhWLlKdtvMgrKzpyCY06xtLAjTmQLURHSlQ==", "type": "package", "path": "microsoft.aspnetcore.metadata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net8.0/Microsoft.AspNetCore.Metadata.dll", "lib/net8.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"sha512": "7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.2": {"sha512": "7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.2.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"sha512": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "type": "package", "path": "microsoft.extensions.configuration.commandline/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"sha512": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"sha512": "EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.1": {"sha512": "L89DLNuimOghjV3tLx0ArFDwVEJD6+uGB3BMCMX01kaLzXkaXHb2021xOMl2QOxUxbdePKUZsUY7n2UUkycjRg==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.1.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.1": {"sha512": "7tYqdPPpAK+3jO9d5LTuCK2VxrEdf85Ol4trUr6ds4jclBecadWZ/RyPCbNjfbN5iGTfUnD/h65TOQuqQv2c+A==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.8.0.1.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"sha512": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"sha512": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/8.0.1": {"sha512": "doVPCUUCY7c6LhBsEfiy3W1bvS7Mi6LkfQMS8nlC22jZWNxBv8VO8bdfeyvpYFst6Kxqk7HBC6lytmEoBssvSQ==", "type": "package", "path": "microsoft.extensions.diagnostics/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.8.0.1.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.1": {"sha512": "elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"sha512": "uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"sha512": "ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "type": "package", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Composite.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Composite.targets", "lib/net462/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net462/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.xml", "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.composite.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"sha512": "6Ev1goLIvggLF6uCs6oZvdr9JM+2b1Zj+4FLdBWNW5iw3tm2BymVIb0yMsjnQTBWL7YUmqVWH3u45hSqOfvuqg==", "type": "package", "path": "microsoft.extensions.fileproviders.embedded/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.props", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.targets", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.props", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.targets", "lib/net462/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net462/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.xml", "microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.embedded.nuspec", "tasks/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.Manifest.Task.dll"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/8.0.1": {"sha512": "bP9EEkHBEfjgYiG8nUaXqMk/ujwJrffOkNPP7onpRMO8R+OUSESSP4xHkCAXgYZ1COP2Q9lXlU5gkMFh20gRuw==", "type": "package", "path": "microsoft.extensions.hosting/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net6.0/Microsoft.Extensions.Hosting.dll", "lib/net6.0/Microsoft.Extensions.Hosting.xml", "lib/net7.0/Microsoft.Extensions.Hosting.dll", "lib/net7.0/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.8.0.1.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.1": {"sha512": "nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/8.0.1": {"sha512": "kDYeKJUzh0qeg/AI+nSr3ffthmXYQTEb0nS9qRC7YhSbbuN4M4NPbaB77AJwtkTnCV9XZ7qYj3dkZaNcyl73EA==", "type": "package", "path": "microsoft.extensions.http/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Http.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Http.targets", "lib/net462/Microsoft.Extensions.Http.dll", "lib/net462/Microsoft.Extensions.Http.xml", "lib/net6.0/Microsoft.Extensions.Http.dll", "lib/net6.0/Microsoft.Extensions.Http.xml", "lib/net7.0/Microsoft.Extensions.Http.dll", "lib/net7.0/Microsoft.Extensions.Http.xml", "lib/net8.0/Microsoft.Extensions.Http.dll", "lib/net8.0/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.8.0.1.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http.Polly/8.0.10": {"sha512": "Of5qq+Xovs6/hDY+n86keBL3Ww1zIIm52eNEHcdW1fvamXIcG0rvbkW1VM4SJNWqmR2fqhFSOLz0wGdBgpZROg==", "type": "package", "path": "microsoft.extensions.http.polly/8.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.Polly.xml", "microsoft.extensions.http.polly.8.0.10.nupkg.sha512", "microsoft.extensions.http.polly.nuspec"]}, "Microsoft.Extensions.Localization/8.0.0": {"sha512": "I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "type": "package", "path": "microsoft.extensions.localization/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.dll", "lib/net462/Microsoft.Extensions.Localization.xml", "lib/net8.0/Microsoft.Extensions.Localization.dll", "lib/net8.0/Microsoft.Extensions.Localization.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.8.0.0.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"sha512": "LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "type": "package", "path": "microsoft.extensions.localization.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net462/Microsoft.Extensions.Localization.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/8.0.1": {"sha512": "4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "type": "package", "path": "microsoft.extensions.logging/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.1.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"sha512": "nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/8.0.1": {"sha512": "QWwTrsgOnJMmn+XUslm8D2H1n3PkP/u/v52FODtyBc/k4W9r3i2vcXXeeX/upnzllJYRRbrzVzT0OclfNJtBJA==", "type": "package", "path": "microsoft.extensions.logging.configuration/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.8.0.1.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/8.0.1": {"sha512": "uzcg/5U2eLyn5LIKlERkdSxw6VPC1yydnOSQiRRWGBGN3kphq3iL4emORzrojScDmxRhv49gp5BI8U3Dz7y4iA==", "type": "package", "path": "microsoft.extensions.logging.console/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net6.0/Microsoft.Extensions.Logging.Console.dll", "lib/net6.0/Microsoft.Extensions.Logging.Console.xml", "lib/net7.0/Microsoft.Extensions.Logging.Console.dll", "lib/net7.0/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.8.0.1.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/8.0.1": {"sha512": "B8hqNuYudC2RB+L/DI33uO4rf5by41fZVdcVL2oZj0UyoAZqnwTwYHp1KafoH4nkl1/23piNeybFFASaV2HkFg==", "type": "package", "path": "microsoft.extensions.logging.debug/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net6.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net6.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net7.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net7.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.8.0.1.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/8.0.1": {"sha512": "ZD1m4GXoxcZeDJIq8qePKj+QAWeQNO/OG8skvrOG8RQfxLp9MAKRoliTc27xanoNUzeqvX5HhS/I7c0BvwAYUg==", "type": "package", "path": "microsoft.extensions.logging.eventlog/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.8.0.1.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/8.0.1": {"sha512": "YMXMAla6B6sEf/SnfZYTty633Ool3AH7KOw2LOaaEqwSo2piK4f7HMtzyc3CNiipDnq1fsUSuG5Oc7ZzpVy8WQ==", "type": "package", "path": "microsoft.extensions.logging.eventsource/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.8.0.1.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.2": {"sha512": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "type": "package", "path": "microsoft.extensions.options/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"sha512": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.0": {"sha512": "N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "type": "package", "path": "microsoft.extensions.primitives/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Nito.AsyncEx.Context/5.1.2": {"sha512": "rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "type": "package", "path": "nito.asyncex.context/5.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.AsyncEx.Context.dll", "lib/net461/Nito.AsyncEx.Context.xml", "lib/netstandard1.3/Nito.AsyncEx.Context.dll", "lib/netstandard1.3/Nito.AsyncEx.Context.xml", "lib/netstandard2.0/Nito.AsyncEx.Context.dll", "lib/netstandard2.0/Nito.AsyncEx.Context.xml", "nito.asyncex.context.5.1.2.nupkg.sha512", "nito.asyncex.context.nuspec"]}, "Nito.AsyncEx.Tasks/5.1.2": {"sha512": "jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "type": "package", "path": "nito.asyncex.tasks/5.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.AsyncEx.Tasks.dll", "lib/net461/Nito.AsyncEx.Tasks.xml", "lib/netstandard1.3/Nito.AsyncEx.Tasks.dll", "lib/netstandard1.3/Nito.AsyncEx.Tasks.xml", "lib/netstandard2.0/Nito.AsyncEx.Tasks.dll", "lib/netstandard2.0/Nito.AsyncEx.Tasks.xml", "nito.asyncex.tasks.5.1.2.nupkg.sha512", "nito.asyncex.tasks.nuspec"]}, "Nito.Disposables/2.2.1": {"sha512": "6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "type": "package", "path": "nito.disposables/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.Disposables.dll", "lib/net461/Nito.Disposables.xml", "lib/netstandard1.0/Nito.Disposables.dll", "lib/netstandard1.0/Nito.Disposables.xml", "lib/netstandard2.0/Nito.Disposables.dll", "lib/netstandard2.0/Nito.Disposables.xml", "lib/netstandard2.1/Nito.Disposables.dll", "lib/netstandard2.1/Nito.Disposables.xml", "nito.disposables.2.2.1.nupkg.sha512", "nito.disposables.nuspec"]}, "NUglify/1.21.0": {"sha512": "9J44o89PRdcS4GOyj0obkLfjCAuJItI4FrNmwALkjRKlzvHVlTB2ALbC9aigIoCMqzy0Xlc0mIVD/jO9WVDHiA==", "type": "package", "path": "nuglify/1.21.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/NUglify.dll", "lib/net35/NUglify.xml", "lib/net40/NUglify.dll", "lib/net40/NUglify.xml", "lib/net5.0/NUglify.dll", "lib/net5.0/NUglify.xml", "lib/netstandard1.3/NUglify.dll", "lib/netstandard1.3/NUglify.xml", "lib/netstandard2.0/NUglify.dll", "lib/netstandard2.0/NUglify.xml", "nuglify.1.21.0.nupkg.sha512", "nuglify.nuspec", "nuglify.png"]}, "Polly/7.2.4": {"sha512": "bw00Ck5sh6ekduDE3mnCo1ohzuad946uslCDEENu3091+6UKnBuKLo4e+yaNcCzXxOZCXWY2gV4a35+K1d4LDA==", "type": "package", "path": "polly/7.2.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net461/Polly.dll", "lib/net461/Polly.pdb", "lib/net461/Polly.xml", "lib/net472/Polly.dll", "lib/net472/Polly.pdb", "lib/net472/Polly.xml", "lib/netstandard1.1/Polly.dll", "lib/netstandard1.1/Polly.pdb", "lib/netstandard1.1/Polly.xml", "lib/netstandard2.0/Polly.dll", "lib/netstandard2.0/Polly.pdb", "lib/netstandard2.0/Polly.xml", "package-icon.png", "polly.7.2.4.nupkg.sha512", "polly.nuspec"]}, "Polly.Extensions.Http/3.0.0": {"sha512": "drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "type": "package", "path": "polly.extensions.http/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.1/Polly.Extensions.Http.dll", "lib/netstandard1.1/Polly.Extensions.Http.xml", "lib/netstandard2.0/Polly.Extensions.Http.dll", "lib/netstandard2.0/Polly.Extensions.Http.xml", "polly.extensions.http.3.0.0.nupkg.sha512", "polly.extensions.http.nuspec"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.EventLog/8.0.1": {"sha512": "n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg==", "type": "package", "path": "system.diagnostics.eventlog/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.1.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.3.5": {"sha512": "G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "type": "package", "path": "system.linq.dynamic.core/1.3.5", "files": [".nupkg.metadata", ".signature.p7s", "PackageReadme.md", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net452/System.Linq.Dynamic.Core.dll", "lib/net452/System.Linq.Dynamic.Core.pdb", "lib/net452/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/net5.0/System.Linq.Dynamic.Core.dll", "lib/net5.0/System.Linq.Dynamic.Core.pdb", "lib/net5.0/System.Linq.Dynamic.Core.xml", "lib/net6.0/System.Linq.Dynamic.Core.dll", "lib/net6.0/System.Linq.Dynamic.Core.pdb", "lib/net6.0/System.Linq.Dynamic.Core.xml", "lib/net7.0/System.Linq.Dynamic.Core.dll", "lib/net7.0/System.Linq.Dynamic.Core.pdb", "lib/net7.0/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/netstandard2.1/System.Linq.Dynamic.Core.dll", "lib/netstandard2.1/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.1/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "logo.png", "system.linq.dynamic.core.1.3.5.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Linq.Queryable/4.3.0": {"sha512": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "type": "package", "path": "system.linq.queryable/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Queryable.dll", "lib/netstandard1.3/System.Linq.Queryable.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Queryable.dll", "ref/netcore50/System.Linq.Queryable.xml", "ref/netcore50/de/System.Linq.Queryable.xml", "ref/netcore50/es/System.Linq.Queryable.xml", "ref/netcore50/fr/System.Linq.Queryable.xml", "ref/netcore50/it/System.Linq.Queryable.xml", "ref/netcore50/ja/System.Linq.Queryable.xml", "ref/netcore50/ko/System.Linq.Queryable.xml", "ref/netcore50/ru/System.Linq.Queryable.xml", "ref/netcore50/zh-hans/System.Linq.Queryable.xml", "ref/netcore50/zh-hant/System.Linq.Queryable.xml", "ref/netstandard1.0/System.Linq.Queryable.dll", "ref/netstandard1.0/System.Linq.Queryable.xml", "ref/netstandard1.0/de/System.Linq.Queryable.xml", "ref/netstandard1.0/es/System.Linq.Queryable.xml", "ref/netstandard1.0/fr/System.Linq.Queryable.xml", "ref/netstandard1.0/it/System.Linq.Queryable.xml", "ref/netstandard1.0/ja/System.Linq.Queryable.xml", "ref/netstandard1.0/ko/System.Linq.Queryable.xml", "ref/netstandard1.0/ru/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hans/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hant/System.Linq.Queryable.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.queryable.4.3.0.nupkg.sha512", "system.linq.queryable.nuspec"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.0": {"sha512": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "type": "package", "path": "system.text.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "TimeZoneConverter/6.1.0": {"sha512": "UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "type": "package", "path": "timezoneconverter/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/TimeZoneConverter.dll", "lib/net462/TimeZoneConverter.xml", "lib/net6.0/TimeZoneConverter.dll", "lib/net6.0/TimeZoneConverter.xml", "lib/netstandard2.0/TimeZoneConverter.dll", "lib/netstandard2.0/TimeZoneConverter.xml", "timezoneconverter.6.1.0.nupkg.sha512", "timezoneconverter.nuspec"]}, "Volo.Abp.Account.Application.Contracts/8.1.1": {"sha512": "9uNJfv9fM//daZoS8eF/XgcTfFn8zYHcpd9fr/TMgfXvHnMDmtKRNRkIga0yuSNa3K4IDvtE501XR2FPOCCIug==", "type": "package", "path": "volo.abp.account.application.contracts/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Account.Application.Contracts.abppkg", "content/Volo.Abp.Account.Application.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Account.Application.Contracts.dll", "lib/net8.0/Volo.Abp.Account.Application.Contracts.pdb", "lib/net8.0/Volo.Abp.Account.Application.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Account.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Account.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Account.Application.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Account.Application.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Account.Application.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Account.Application.Contracts.xml", "volo.abp.account.application.contracts.8.1.1.nupkg.sha512", "volo.abp.account.application.contracts.nuspec"]}, "Volo.Abp.Account.HttpApi.Client/8.1.1": {"sha512": "gbZxtwxH4uQCh3b87JU+MciHSQLO6hgWfPVKH+6/dcGGOpNRK7GGYrvQyYFaShCPANxxws8pcNLGnJ7j1DmY3w==", "type": "package", "path": "volo.abp.account.httpapi.client/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Account.HttpApi.Client.abppkg", "content/Volo.Abp.Account.HttpApi.Client.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Account.HttpApi.Client.dll", "lib/net8.0/Volo.Abp.Account.HttpApi.Client.pdb", "lib/net8.0/Volo.Abp.Account.HttpApi.Client.xml", "lib/netstandard2.0/Volo.Abp.Account.HttpApi.Client.dll", "lib/netstandard2.0/Volo.Abp.Account.HttpApi.Client.pdb", "lib/netstandard2.0/Volo.Abp.Account.HttpApi.Client.xml", "lib/netstandard2.1/Volo.Abp.Account.HttpApi.Client.dll", "lib/netstandard2.1/Volo.Abp.Account.HttpApi.Client.pdb", "lib/netstandard2.1/Volo.Abp.Account.HttpApi.Client.xml", "volo.abp.account.httpapi.client.8.1.1.nupkg.sha512", "volo.abp.account.httpapi.client.nuspec"]}, "Volo.Abp.Auditing.Contracts/8.1.1": {"sha512": "xUk7lOZxcJoQBjPCIiYxZO2D2tavHXpYCIwH3SfG4xOOwb4ro1TqBKCPMki9ermX0yKmaL2eM55wsXT7RRxuLg==", "type": "package", "path": "volo.abp.auditing.contracts/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Auditing.Contracts.abppkg", "content/Volo.Abp.Auditing.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Auditing.Contracts.dll", "lib/net8.0/Volo.Abp.Auditing.Contracts.pdb", "lib/net8.0/Volo.Abp.Auditing.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.xml", "volo.abp.auditing.contracts.8.1.1.nupkg.sha512", "volo.abp.auditing.contracts.nuspec"]}, "Volo.Abp.Authorization/8.1.1": {"sha512": "O/SsMfTekNmsP6+jP4So1BY3bPyEGDLpwxD4frLAea+/zVg6c2ENldKjAOutTQJX2TcCpD4w7XEZAVhQ+HTd9Q==", "type": "package", "path": "volo.abp.authorization/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Authorization.abppkg", "content/Volo.Abp.Authorization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Authorization.dll", "lib/net8.0/Volo.Abp.Authorization.pdb", "lib/net8.0/Volo.Abp.Authorization.xml", "lib/netstandard2.0/Volo.Abp.Authorization.dll", "lib/netstandard2.0/Volo.Abp.Authorization.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.xml", "lib/netstandard2.1/Volo.Abp.Authorization.dll", "lib/netstandard2.1/Volo.Abp.Authorization.pdb", "lib/netstandard2.1/Volo.Abp.Authorization.xml", "volo.abp.authorization.8.1.1.nupkg.sha512", "volo.abp.authorization.nuspec"]}, "Volo.Abp.Authorization.Abstractions/8.1.1": {"sha512": "dBZjyo3xXwn/XFgzIfUk4W8sICOsCKbhk1Ug4n5a8KPG17fcoovFFug4ACcY80TMGsrgQ8TaibZsNRgLXPHnQw==", "type": "package", "path": "volo.abp.authorization.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Authorization.Abstractions.abppkg", "content/Volo.Abp.Authorization.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Authorization.Abstractions.dll", "lib/net8.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/net8.0/Volo.Abp.Authorization.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.xml", "volo.abp.authorization.abstractions.8.1.1.nupkg.sha512", "volo.abp.authorization.abstractions.nuspec"]}, "Volo.Abp.BackgroundWorkers/8.1.1": {"sha512": "86+vkzHDSKio95VO93bNmpuuRe+usPuQDw+IoRQbwI/4raqFoSyiCGKvGOp/Fnm4pIIutFvqBPpmloexpHdWig==", "type": "package", "path": "volo.abp.backgroundworkers/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.BackgroundWorkers.abppkg", "content/Volo.Abp.BackgroundWorkers.abppkg.analyze.json", "lib/net8.0/Volo.Abp.BackgroundWorkers.dll", "lib/net8.0/Volo.Abp.BackgroundWorkers.pdb", "lib/net8.0/Volo.Abp.BackgroundWorkers.xml", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.dll", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.pdb", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.xml", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.dll", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.pdb", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.xml", "volo.abp.backgroundworkers.8.1.1.nupkg.sha512", "volo.abp.backgroundworkers.nuspec"]}, "Volo.Abp.Caching/8.1.1": {"sha512": "C+GEspeakR12qItw0QVQah+e4p5TPSPe4My5CyPIrzZwN5YKa3pEYvytjGpGvQ4+hcbPkHh5HN2lxLg44G0acQ==", "type": "package", "path": "volo.abp.caching/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Caching.abppkg", "content/Volo.Abp.Caching.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Caching.dll", "lib/net8.0/Volo.Abp.Caching.pdb", "lib/net8.0/Volo.Abp.Caching.xml", "lib/netstandard2.0/Volo.Abp.Caching.dll", "lib/netstandard2.0/Volo.Abp.Caching.pdb", "lib/netstandard2.0/Volo.Abp.Caching.xml", "lib/netstandard2.1/Volo.Abp.Caching.dll", "lib/netstandard2.1/Volo.Abp.Caching.pdb", "lib/netstandard2.1/Volo.Abp.Caching.xml", "volo.abp.caching.8.1.1.nupkg.sha512", "volo.abp.caching.nuspec"]}, "Volo.Abp.Castle.Core/8.1.1": {"sha512": "0IRz2H9bWTfl5vNzVxnzUx0tx2q9/YpAgKfX7L5cm5YZSY/S2Cwx5VMjNpVxG9uFf6CtPMMTWWqTF5JyW83YVg==", "type": "package", "path": "volo.abp.castle.core/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Castle.Core.abppkg", "content/Volo.Abp.Castle.Core.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Castle.Core.dll", "lib/net8.0/Volo.Abp.Castle.Core.pdb", "lib/net8.0/Volo.Abp.Castle.Core.xml", "lib/netstandard2.0/Volo.Abp.Castle.Core.dll", "lib/netstandard2.0/Volo.Abp.Castle.Core.pdb", "lib/netstandard2.0/Volo.Abp.Castle.Core.xml", "lib/netstandard2.1/Volo.Abp.Castle.Core.dll", "lib/netstandard2.1/Volo.Abp.Castle.Core.pdb", "lib/netstandard2.1/Volo.Abp.Castle.Core.xml", "volo.abp.castle.core.8.1.1.nupkg.sha512", "volo.abp.castle.core.nuspec"]}, "Volo.Abp.Core/8.1.1": {"sha512": "sNtdDe2/EWSz5tyHHWTJv8s2znVHVEP3019F6vXkoPYeL3kNYjSMX1W+ibH/TP5jmowaRZc9AZbfjHUb2bnHkQ==", "type": "package", "path": "volo.abp.core/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Core.abppkg", "content/Volo.Abp.Core.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Core.dll", "lib/net8.0/Volo.Abp.Core.pdb", "lib/net8.0/Volo.Abp.Core.xml", "lib/netstandard2.0/Volo.Abp.Core.dll", "lib/netstandard2.0/Volo.Abp.Core.pdb", "lib/netstandard2.0/Volo.Abp.Core.xml", "lib/netstandard2.1/Volo.Abp.Core.dll", "lib/netstandard2.1/Volo.Abp.Core.pdb", "lib/netstandard2.1/Volo.Abp.Core.xml", "volo.abp.core.8.1.1.nupkg.sha512", "volo.abp.core.nuspec"]}, "Volo.Abp.Data/8.1.1": {"sha512": "FkGUAlgMFel85sUAVLBSI2K8nt9eEbyZCp9GT8ZVxGLKHnvUNfotefb7MJZSYSpwpmA1Xg8REkd2IQsqsGgzbw==", "type": "package", "path": "volo.abp.data/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Data.abppkg", "content/Volo.Abp.Data.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Data.dll", "lib/net8.0/Volo.Abp.Data.pdb", "lib/net8.0/Volo.Abp.Data.xml", "lib/netstandard2.0/Volo.Abp.Data.dll", "lib/netstandard2.0/Volo.Abp.Data.pdb", "lib/netstandard2.0/Volo.Abp.Data.xml", "lib/netstandard2.1/Volo.Abp.Data.dll", "lib/netstandard2.1/Volo.Abp.Data.pdb", "lib/netstandard2.1/Volo.Abp.Data.xml", "volo.abp.data.8.1.1.nupkg.sha512", "volo.abp.data.nuspec"]}, "Volo.Abp.Ddd.Application.Contracts/8.1.1": {"sha512": "nHy1J2jvP9WxIl5gZBLjQt+oK5DfKVuKDLaLpimiC7LNiRI4vLMyCe1YWJQmSmtMd9QYNpAKCmoBsAB5X1IR4g==", "type": "package", "path": "volo.abp.ddd.application.contracts/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Ddd.Application.Contracts.abppkg", "content/Volo.Abp.Ddd.Application.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.xml", "volo.abp.ddd.application.contracts.8.1.1.nupkg.sha512", "volo.abp.ddd.application.contracts.nuspec"]}, "Volo.Abp.DistributedLocking.Abstractions/8.1.1": {"sha512": "Tdnb/kPLbDmmU5ullLX//3gIBAJ0Et7OuYADydW6a22NHwHsxPhs9w++UEsjkxFKz+0Fy97MkJLOt65xcNjAZA==", "type": "package", "path": "volo.abp.distributedlocking.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.DistributedLocking.Abstractions.abppkg", "content/Volo.Abp.DistributedLocking.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.xml", "volo.abp.distributedlocking.abstractions.8.1.1.nupkg.sha512", "volo.abp.distributedlocking.abstractions.nuspec"]}, "Volo.Abp.EventBus/8.1.1": {"sha512": "h/SrkDEK6t54x4A6HnRzNqco+5oB5rf8V8rRJExFDgUxte+OuAEneDcFUm41HtOkIXgDKQiAeYHSDoIe63OnIQ==", "type": "package", "path": "volo.abp.eventbus/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.EventBus.abppkg", "content/Volo.Abp.EventBus.abppkg.analyze.json", "lib/net8.0/Volo.Abp.EventBus.dll", "lib/net8.0/Volo.Abp.EventBus.pdb", "lib/net8.0/Volo.Abp.EventBus.xml", "lib/netstandard2.0/Volo.Abp.EventBus.dll", "lib/netstandard2.0/Volo.Abp.EventBus.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.xml", "lib/netstandard2.1/Volo.Abp.EventBus.dll", "lib/netstandard2.1/Volo.Abp.EventBus.pdb", "lib/netstandard2.1/Volo.Abp.EventBus.xml", "volo.abp.eventbus.8.1.1.nupkg.sha512", "volo.abp.eventbus.nuspec"]}, "Volo.Abp.EventBus.Abstractions/8.1.1": {"sha512": "CX13/KxRTEJ9aio/dQd/gtTy00pq8f93p4YNbrmvhAuw9NpWc4rRPuNkRdm7TkpvXX2rezQBzNEZYzanB41KIA==", "type": "package", "path": "volo.abp.eventbus.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.EventBus.Abstractions.abppkg", "content/Volo.Abp.EventBus.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.EventBus.Abstractions.dll", "lib/net8.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/net8.0/Volo.Abp.EventBus.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.xml", "volo.abp.eventbus.abstractions.8.1.1.nupkg.sha512", "volo.abp.eventbus.abstractions.nuspec"]}, "Volo.Abp.ExceptionHandling/8.1.1": {"sha512": "AZhRYR5THspYRyLFc7lnLPNQkI0Bn4j3tcsXJAiwpQkvPmivLgOL66C9gGBuM42Q9v6Tk6Y6RIy/G70pJbKc9g==", "type": "package", "path": "volo.abp.exceptionhandling/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ExceptionHandling.abppkg", "content/Volo.Abp.ExceptionHandling.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ExceptionHandling.dll", "lib/net8.0/Volo.Abp.ExceptionHandling.pdb", "lib/net8.0/Volo.Abp.ExceptionHandling.xml", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.xml", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.xml", "volo.abp.exceptionhandling.8.1.1.nupkg.sha512", "volo.abp.exceptionhandling.nuspec"]}, "Volo.Abp.Features/8.1.1": {"sha512": "bMvjYmnxW4NTWwBgGjafSuIBgVdGkwy2xSO0ZIqbcxXTubnnw1C8aiOTZzg+pTobTfkJPU4GdimyRchcq+K3Jg==", "type": "package", "path": "volo.abp.features/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Features.abppkg", "content/Volo.Abp.Features.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Features.dll", "lib/net8.0/Volo.Abp.Features.pdb", "lib/net8.0/Volo.Abp.Features.xml", "lib/netstandard2.0/Volo.Abp.Features.dll", "lib/netstandard2.0/Volo.Abp.Features.pdb", "lib/netstandard2.0/Volo.Abp.Features.xml", "lib/netstandard2.1/Volo.Abp.Features.dll", "lib/netstandard2.1/Volo.Abp.Features.pdb", "lib/netstandard2.1/Volo.Abp.Features.xml", "volo.abp.features.8.1.1.nupkg.sha512", "volo.abp.features.nuspec"]}, "Volo.Abp.Guids/8.1.1": {"sha512": "3nnNnffKkIKrzl14xlhaPads4GVzPneFSf59w+wKkzTjnbC6cmsWmC+6W7qcVAYFpwioXUpBQLMZj1BQgREwew==", "type": "package", "path": "volo.abp.guids/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Guids.abppkg", "content/Volo.Abp.Guids.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Guids.dll", "lib/net8.0/Volo.Abp.Guids.pdb", "lib/net8.0/Volo.Abp.Guids.xml", "lib/netstandard2.0/Volo.Abp.Guids.dll", "lib/netstandard2.0/Volo.Abp.Guids.pdb", "lib/netstandard2.0/Volo.Abp.Guids.xml", "lib/netstandard2.1/Volo.Abp.Guids.dll", "lib/netstandard2.1/Volo.Abp.Guids.pdb", "lib/netstandard2.1/Volo.Abp.Guids.xml", "volo.abp.guids.8.1.1.nupkg.sha512", "volo.abp.guids.nuspec"]}, "Volo.Abp.Http/8.1.1": {"sha512": "PLhOHstLHOtdr03wXVod+mi7MoxKhOJ1RCjJqk71XlGQWLl4YhdcXfrG+rH3+vkRkAn4XyYa/n50InmqemTdZg==", "type": "package", "path": "volo.abp.http/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.abppkg", "content/Volo.Abp.Http.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Http.dll", "lib/net8.0/Volo.Abp.Http.pdb", "lib/net8.0/Volo.Abp.Http.xml", "lib/netstandard2.0/Volo.Abp.Http.dll", "lib/netstandard2.0/Volo.Abp.Http.pdb", "lib/netstandard2.0/Volo.Abp.Http.xml", "lib/netstandard2.1/Volo.Abp.Http.dll", "lib/netstandard2.1/Volo.Abp.Http.pdb", "lib/netstandard2.1/Volo.Abp.Http.xml", "volo.abp.http.8.1.1.nupkg.sha512", "volo.abp.http.nuspec"]}, "Volo.Abp.Http.Abstractions/8.1.1": {"sha512": "b2cxbkFPm3tqb7g7xXfpedRnjuuOHytlpLsdWlQ5rS5sWig/3f6uxFSRDoh9DJU34YGa5Gctd6I7IaSYvdMWog==", "type": "package", "path": "volo.abp.http.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.Abstractions.abppkg", "content/Volo.Abp.Http.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Http.Abstractions.dll", "lib/net8.0/Volo.Abp.Http.Abstractions.pdb", "lib/net8.0/Volo.Abp.Http.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.xml", "volo.abp.http.abstractions.8.1.1.nupkg.sha512", "volo.abp.http.abstractions.nuspec"]}, "Volo.Abp.Http.Client/8.1.1": {"sha512": "3oM7eGHDXu9TKuAv5hpw8QqhGV5VDPrcCLkB31Apl9xQzBY4hawhaVAoidkLk/dfMQ8i69yyD+9yGTco7TavBg==", "type": "package", "path": "volo.abp.http.client/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.Client.abppkg", "content/Volo.Abp.Http.Client.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Http.Client.dll", "lib/net8.0/Volo.Abp.Http.Client.pdb", "lib/net8.0/Volo.Abp.Http.Client.xml", "lib/netstandard2.0/Volo.Abp.Http.Client.dll", "lib/netstandard2.0/Volo.Abp.Http.Client.pdb", "lib/netstandard2.0/Volo.Abp.Http.Client.xml", "lib/netstandard2.1/Volo.Abp.Http.Client.dll", "lib/netstandard2.1/Volo.Abp.Http.Client.pdb", "lib/netstandard2.1/Volo.Abp.Http.Client.xml", "volo.abp.http.client.8.1.1.nupkg.sha512", "volo.abp.http.client.nuspec"]}, "Volo.Abp.Http.Client.IdentityModel/8.1.1": {"sha512": "Ss1tib71IUuyZ7PeRHWa64K11PJ52GgU5FyF+hWhvCSfH+tok9DwnZVVb1qhHIKgInoFcGwrZ9TmRcbJU2ZgdA==", "type": "package", "path": "volo.abp.http.client.identitymodel/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.Client.IdentityModel.abppkg", "lib/net8.0/Volo.Abp.Http.Client.IdentityModel.dll", "lib/net8.0/Volo.Abp.Http.Client.IdentityModel.pdb", "lib/net8.0/Volo.Abp.Http.Client.IdentityModel.xml", "lib/netstandard2.0/Volo.Abp.Http.Client.IdentityModel.dll", "lib/netstandard2.0/Volo.Abp.Http.Client.IdentityModel.pdb", "lib/netstandard2.0/Volo.Abp.Http.Client.IdentityModel.xml", "lib/netstandard2.1/Volo.Abp.Http.Client.IdentityModel.dll", "lib/netstandard2.1/Volo.Abp.Http.Client.IdentityModel.pdb", "lib/netstandard2.1/Volo.Abp.Http.Client.IdentityModel.xml", "volo.abp.http.client.identitymodel.8.1.1.nupkg.sha512", "volo.abp.http.client.identitymodel.nuspec"]}, "Volo.Abp.Identity.Application.Contracts/8.1.1": {"sha512": "BBhSossoDSC2Iq24l4wghHT0Mu+PHByXXKR7j/HczSMD0Y8oV9XQXNscUOavNznp3VXFi9avuq5xqNGqcpIuuQ==", "type": "package", "path": "volo.abp.identity.application.contracts/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Identity.Application.Contracts.abppkg", "content/Volo.Abp.Identity.Application.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Identity.Application.Contracts.dll", "lib/net8.0/Volo.Abp.Identity.Application.Contracts.pdb", "lib/net8.0/Volo.Abp.Identity.Application.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Identity.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Identity.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Identity.Application.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Identity.Application.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Identity.Application.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Identity.Application.Contracts.xml", "volo.abp.identity.application.contracts.8.1.1.nupkg.sha512", "volo.abp.identity.application.contracts.nuspec"]}, "Volo.Abp.Identity.Domain.Shared/8.1.1": {"sha512": "+lvLGuzrwCGQBsx6od6gK/2x5nL6szg6AOS63MtlOn3ZFSDZbkreUSoi2H/AKNLAgN5NPBjwLNEWdAgPu6t2Lw==", "type": "package", "path": "volo.abp.identity.domain.shared/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Identity.Domain.Shared.abppkg", "content/Volo.Abp.Identity.Domain.Shared.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll", "lib/net8.0/Volo.Abp.Identity.Domain.Shared.pdb", "lib/net8.0/Volo.Abp.Identity.Domain.Shared.xml", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.Identity.Domain.Shared.xml", "lib/netstandard2.1/Volo.Abp.Identity.Domain.Shared.dll", "lib/netstandard2.1/Volo.Abp.Identity.Domain.Shared.pdb", "lib/netstandard2.1/Volo.Abp.Identity.Domain.Shared.xml", "volo.abp.identity.domain.shared.8.1.1.nupkg.sha512", "volo.abp.identity.domain.shared.nuspec"]}, "Volo.Abp.Identity.HttpApi.Client/8.1.1": {"sha512": "Jv1h0CnDUFh8AuV9TS52Ui93kSIbiu6KpJu9IeCka+fw1LgrMqpNL6l3l2gZqCNCrUmOE4g0+4eosL2vf/B/ug==", "type": "package", "path": "volo.abp.identity.httpapi.client/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Identity.HttpApi.Client.abppkg", "content/Volo.Abp.Identity.HttpApi.Client.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Identity.HttpApi.Client.dll", "lib/net8.0/Volo.Abp.Identity.HttpApi.Client.pdb", "lib/net8.0/Volo.Abp.Identity.HttpApi.Client.xml", "lib/netstandard2.0/Volo.Abp.Identity.HttpApi.Client.dll", "lib/netstandard2.0/Volo.Abp.Identity.HttpApi.Client.pdb", "lib/netstandard2.0/Volo.Abp.Identity.HttpApi.Client.xml", "lib/netstandard2.1/Volo.Abp.Identity.HttpApi.Client.dll", "lib/netstandard2.1/Volo.Abp.Identity.HttpApi.Client.pdb", "lib/netstandard2.1/Volo.Abp.Identity.HttpApi.Client.xml", "volo.abp.identity.httpapi.client.8.1.1.nupkg.sha512", "volo.abp.identity.httpapi.client.nuspec"]}, "Volo.Abp.IdentityModel/8.1.1": {"sha512": "5GNGT/i435qPekJdBKoDd8Y2wx/h35vByRbsKeN8EcbfsOCRe6oPQftzC/ElqCaGRfpExb7fkLaO0CrElIl9Cg==", "type": "package", "path": "volo.abp.identitymodel/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.IdentityModel.abppkg", "lib/net8.0/Volo.Abp.IdentityModel.dll", "lib/net8.0/Volo.Abp.IdentityModel.pdb", "lib/net8.0/Volo.Abp.IdentityModel.xml", "lib/netstandard2.0/Volo.Abp.IdentityModel.dll", "lib/netstandard2.0/Volo.Abp.IdentityModel.pdb", "lib/netstandard2.0/Volo.Abp.IdentityModel.xml", "lib/netstandard2.1/Volo.Abp.IdentityModel.dll", "lib/netstandard2.1/Volo.Abp.IdentityModel.pdb", "lib/netstandard2.1/Volo.Abp.IdentityModel.xml", "volo.abp.identitymodel.8.1.1.nupkg.sha512", "volo.abp.identitymodel.nuspec"]}, "Volo.Abp.Json/8.1.1": {"sha512": "PIEnmql9AbeGTph1kOhCnUqB+f00iFaujFwxp68Cx/5d3D7PO5zpzIgLVlASV29++BTmbMZaSzNctEozj0pdrg==", "type": "package", "path": "volo.abp.json/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.abppkg", "content/Volo.Abp.Json.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.dll", "lib/net8.0/Volo.Abp.Json.pdb", "lib/net8.0/Volo.Abp.Json.xml", "lib/netstandard2.0/Volo.Abp.Json.dll", "lib/netstandard2.0/Volo.Abp.Json.pdb", "lib/netstandard2.0/Volo.Abp.Json.xml", "lib/netstandard2.1/Volo.Abp.Json.dll", "lib/netstandard2.1/Volo.Abp.Json.pdb", "lib/netstandard2.1/Volo.Abp.Json.xml", "volo.abp.json.8.1.1.nupkg.sha512", "volo.abp.json.nuspec"]}, "Volo.Abp.Json.Abstractions/8.1.1": {"sha512": "vn4bXF9KWkcKOz3MjOrn3blA1GAxd0ePCBW29LQHg6GKTUuzvhcV75vNb5NdspErDBbXSL8tY0ujr9/kvCmD+A==", "type": "package", "path": "volo.abp.json.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.Abstractions.abppkg", "content/Volo.Abp.Json.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.Abstractions.dll", "lib/net8.0/Volo.Abp.Json.Abstractions.pdb", "lib/net8.0/Volo.Abp.Json.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.xml", "volo.abp.json.abstractions.8.1.1.nupkg.sha512", "volo.abp.json.abstractions.nuspec"]}, "Volo.Abp.Json.SystemTextJson/8.1.1": {"sha512": "xtnaPqGReRFFTTiGy5QaMB3kNqd2Ydz9VTqrGouLvc1AaqUF9+KhOOCh9kVA18HEqESrm5VxKqpS2ZvRnEEHvw==", "type": "package", "path": "volo.abp.json.systemtextjson/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.SystemTextJson.abppkg", "content/Volo.Abp.Json.SystemTextJson.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.SystemTextJson.dll", "lib/net8.0/Volo.Abp.Json.SystemTextJson.pdb", "lib/net8.0/Volo.Abp.Json.SystemTextJson.xml", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.dll", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.pdb", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.xml", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.dll", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.pdb", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.xml", "volo.abp.json.systemtextjson.8.1.1.nupkg.sha512", "volo.abp.json.systemtextjson.nuspec"]}, "Volo.Abp.Localization/8.1.1": {"sha512": "QECnrtfqsPIlVYHVsa4nilgVTnQbEqmOEjd2gD0oKOEdXQd7iIAhYMHeTbvSO0nwXkq9FztAf2qmCRMDtrbUjw==", "type": "package", "path": "volo.abp.localization/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Localization.abppkg", "content/Volo.Abp.Localization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Localization.dll", "lib/net8.0/Volo.Abp.Localization.pdb", "lib/net8.0/Volo.Abp.Localization.xml", "lib/netstandard2.0/Volo.Abp.Localization.dll", "lib/netstandard2.0/Volo.Abp.Localization.pdb", "lib/netstandard2.0/Volo.Abp.Localization.xml", "lib/netstandard2.1/Volo.Abp.Localization.dll", "lib/netstandard2.1/Volo.Abp.Localization.pdb", "lib/netstandard2.1/Volo.Abp.Localization.xml", "volo.abp.localization.8.1.1.nupkg.sha512", "volo.abp.localization.nuspec"]}, "Volo.Abp.Localization.Abstractions/8.1.1": {"sha512": "Or7NaSIj0wizPtguGUeWW7stoaXUpGr61Y3MRtYjmjIGFxAcrIuX3drlwGK9aB5m6Xt5xQNVkqh4Yy8kEkmNVQ==", "type": "package", "path": "volo.abp.localization.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Localization.Abstractions.abppkg", "content/Volo.Abp.Localization.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Localization.Abstractions.dll", "lib/net8.0/Volo.Abp.Localization.Abstractions.pdb", "lib/net8.0/Volo.Abp.Localization.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.xml", "volo.abp.localization.abstractions.8.1.1.nupkg.sha512", "volo.abp.localization.abstractions.nuspec"]}, "Volo.Abp.Minify/8.1.1": {"sha512": "jigvjlYMCjMM1nb6BfB+/j7fh0YUwIriV6hZfX5/7prIwObt5DoxqLwMtzhjaE+GVbAVT3LzUWPd/JZMLHntHA==", "type": "package", "path": "volo.abp.minify/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Minify.abppkg", "content/Volo.Abp.Minify.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Minify.dll", "lib/net8.0/Volo.Abp.Minify.pdb", "lib/net8.0/Volo.Abp.Minify.xml", "lib/netstandard2.0/Volo.Abp.Minify.dll", "lib/netstandard2.0/Volo.Abp.Minify.pdb", "lib/netstandard2.0/Volo.Abp.Minify.xml", "lib/netstandard2.1/Volo.Abp.Minify.dll", "lib/netstandard2.1/Volo.Abp.Minify.pdb", "lib/netstandard2.1/Volo.Abp.Minify.xml", "volo.abp.minify.8.1.1.nupkg.sha512", "volo.abp.minify.nuspec"]}, "Volo.Abp.MultiTenancy/8.1.1": {"sha512": "KRh2YuKoFVwUhe0bX6If+ukXGLnez0IudjocwwUTLJiBZ1IWjMb70OBbEy03LunIuvft/3i5m7uwIqdDBYKeVA==", "type": "package", "path": "volo.abp.multitenancy/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.MultiTenancy.abppkg", "content/Volo.Abp.MultiTenancy.abppkg.analyze.json", "lib/net8.0/Volo.Abp.MultiTenancy.dll", "lib/net8.0/Volo.Abp.MultiTenancy.pdb", "lib/net8.0/Volo.Abp.MultiTenancy.xml", "lib/netstandard2.0/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.xml", "lib/netstandard2.1/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.1/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.1/Volo.Abp.MultiTenancy.xml", "volo.abp.multitenancy.8.1.1.nupkg.sha512", "volo.abp.multitenancy.nuspec"]}, "Volo.Abp.MultiTenancy.Abstractions/8.1.1": {"sha512": "Vvm4oQsq+uRe29VgUEDiCe8eb2ML2dppcc7YNM1tiiNrDAUBG4lYSiBBvUUABXzS+jS/P1zmkEieU07CFaQSqw==", "type": "package", "path": "volo.abp.multitenancy.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.xml", "volo.abp.multitenancy.abstractions.8.1.1.nupkg.sha512", "volo.abp.multitenancy.abstractions.nuspec"]}, "Volo.Abp.ObjectExtending/8.1.1": {"sha512": "N0K8/fu1xpXcWl2CBCHvf8JP5t9wMt9YMlCjGzaGKP2Y/WQMWtN41yyEeKxYdKCnfcLQ8ZNffZQRPAX6hQKCkQ==", "type": "package", "path": "volo.abp.objectextending/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ObjectExtending.abppkg", "content/Volo.Abp.ObjectExtending.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ObjectExtending.dll", "lib/net8.0/Volo.Abp.ObjectExtending.pdb", "lib/net8.0/Volo.Abp.ObjectExtending.xml", "lib/netstandard2.0/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.0/Volo.Abp.ObjectExtending.pdb", "lib/netstandard2.0/Volo.Abp.ObjectExtending.xml", "lib/netstandard2.1/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.1/Volo.Abp.ObjectExtending.pdb", "lib/netstandard2.1/Volo.Abp.ObjectExtending.xml", "volo.abp.objectextending.8.1.1.nupkg.sha512", "volo.abp.objectextending.nuspec"]}, "Volo.Abp.PermissionManagement.Application.Contracts/8.1.1": {"sha512": "YUerxRU7UQptEGqaBbQ+Kx2Vxdm/bYUn/K07vNuNq9xSwLXzDocMPO6taGKZ1oEs8o0Nnmq/QgKo7f5u7bB4qQ==", "type": "package", "path": "volo.abp.permissionmanagement.application.contracts/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.PermissionManagement.Application.Contracts.abppkg", "content/Volo.Abp.PermissionManagement.Application.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.dll", "lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.pdb", "lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.xml", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Application.Contracts.xml", "lib/netstandard2.1/Volo.Abp.PermissionManagement.Application.Contracts.dll", "lib/netstandard2.1/Volo.Abp.PermissionManagement.Application.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.PermissionManagement.Application.Contracts.xml", "volo.abp.permissionmanagement.application.contracts.8.1.1.nupkg.sha512", "volo.abp.permissionmanagement.application.contracts.nuspec"]}, "Volo.Abp.PermissionManagement.Domain.Shared/8.1.1": {"sha512": "/dXxzRCbr+WMAuuoTc0KrJh4b/JIXtGpkI8nnsHOg5LdzmMF84AnNEM7ZwssQm0slsEJzhfjDMuUrEK2aWRhVw==", "type": "package", "path": "volo.abp.permissionmanagement.domain.shared/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.PermissionManagement.Domain.Shared.abppkg", "content/Volo.Abp.PermissionManagement.Domain.Shared.abppkg.analyze.json", "lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.dll", "lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.pdb", "lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.xml", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.PermissionManagement.Domain.Shared.xml", "lib/netstandard2.1/Volo.Abp.PermissionManagement.Domain.Shared.dll", "lib/netstandard2.1/Volo.Abp.PermissionManagement.Domain.Shared.pdb", "lib/netstandard2.1/Volo.Abp.PermissionManagement.Domain.Shared.xml", "volo.abp.permissionmanagement.domain.shared.8.1.1.nupkg.sha512", "volo.abp.permissionmanagement.domain.shared.nuspec"]}, "Volo.Abp.RemoteServices/8.1.1": {"sha512": "k4uHXPbFF2lDN4IhSOMHTGcsrgLW6XkaUPKSWTXJE//8ELDg6tu9vRLiTZhuUGIXvrSM35v488ap9pyhyL283Q==", "type": "package", "path": "volo.abp.remoteservices/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.RemoteServices.abppkg", "content/Volo.Abp.RemoteServices.abppkg.analyze.json", "lib/net8.0/Volo.Abp.RemoteServices.dll", "lib/net8.0/Volo.Abp.RemoteServices.pdb", "lib/net8.0/Volo.Abp.RemoteServices.xml", "lib/netstandard2.0/Volo.Abp.RemoteServices.dll", "lib/netstandard2.0/Volo.Abp.RemoteServices.pdb", "lib/netstandard2.0/Volo.Abp.RemoteServices.xml", "lib/netstandard2.1/Volo.Abp.RemoteServices.dll", "lib/netstandard2.1/Volo.Abp.RemoteServices.pdb", "lib/netstandard2.1/Volo.Abp.RemoteServices.xml", "volo.abp.remoteservices.8.1.1.nupkg.sha512", "volo.abp.remoteservices.nuspec"]}, "Volo.Abp.Security/8.1.1": {"sha512": "UfC7k+F5U7mj1uBwBEfbGAbjbyeuJXxPLbYtlEPqBztXBGM9VnaQ2/gLN3vdgfJiU0TDx6fmbZJm5lCzVclGyQ==", "type": "package", "path": "volo.abp.security/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Security.abppkg", "content/Volo.Abp.Security.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Security.dll", "lib/net8.0/Volo.Abp.Security.pdb", "lib/net8.0/Volo.Abp.Security.xml", "lib/netstandard2.0/Volo.Abp.Security.dll", "lib/netstandard2.0/Volo.Abp.Security.pdb", "lib/netstandard2.0/Volo.Abp.Security.xml", "lib/netstandard2.1/Volo.Abp.Security.dll", "lib/netstandard2.1/Volo.Abp.Security.pdb", "lib/netstandard2.1/Volo.Abp.Security.xml", "volo.abp.security.8.1.1.nupkg.sha512", "volo.abp.security.nuspec"]}, "Volo.Abp.Serialization/8.1.1": {"sha512": "dNm7frlMg66PckzHtlXqu3ow7ItWcC1NqE+WKT6NCuSC6MEJhiJj5phn9VzKgjbOUYtjlNVbO1QIAYOoVd1AYg==", "type": "package", "path": "volo.abp.serialization/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Serialization.abppkg", "content/Volo.Abp.Serialization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Serialization.dll", "lib/net8.0/Volo.Abp.Serialization.pdb", "lib/net8.0/Volo.Abp.Serialization.xml", "lib/netstandard2.0/Volo.Abp.Serialization.dll", "lib/netstandard2.0/Volo.Abp.Serialization.pdb", "lib/netstandard2.0/Volo.Abp.Serialization.xml", "lib/netstandard2.1/Volo.Abp.Serialization.dll", "lib/netstandard2.1/Volo.Abp.Serialization.pdb", "lib/netstandard2.1/Volo.Abp.Serialization.xml", "volo.abp.serialization.8.1.1.nupkg.sha512", "volo.abp.serialization.nuspec"]}, "Volo.Abp.Settings/8.1.1": {"sha512": "UT+BsKnvB2y35QCi5+TtFekw66/j8o8GelLLWOwf99uqfi3Xk9ntSRtmvXP/vDij9Kyv/ReGWg/E/kwwqQIblg==", "type": "package", "path": "volo.abp.settings/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Settings.abppkg", "content/Volo.Abp.Settings.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Settings.dll", "lib/net8.0/Volo.Abp.Settings.pdb", "lib/net8.0/Volo.Abp.Settings.xml", "lib/netstandard2.0/Volo.Abp.Settings.dll", "lib/netstandard2.0/Volo.Abp.Settings.pdb", "lib/netstandard2.0/Volo.Abp.Settings.xml", "lib/netstandard2.1/Volo.Abp.Settings.dll", "lib/netstandard2.1/Volo.Abp.Settings.pdb", "lib/netstandard2.1/Volo.Abp.Settings.xml", "volo.abp.settings.8.1.1.nupkg.sha512", "volo.abp.settings.nuspec"]}, "Volo.Abp.Threading/8.1.1": {"sha512": "mMUfVsOoHE5c7CgYQKacZZoEV7+i0HepXUxiBjUfS+mwAu6KS/SDUN9KMFWtRtIbqYycRObTF+LdAQFr8EDq0Q==", "type": "package", "path": "volo.abp.threading/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Threading.abppkg", "content/Volo.Abp.Threading.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Threading.dll", "lib/net8.0/Volo.Abp.Threading.pdb", "lib/net8.0/Volo.Abp.Threading.xml", "lib/netstandard2.0/Volo.Abp.Threading.dll", "lib/netstandard2.0/Volo.Abp.Threading.pdb", "lib/netstandard2.0/Volo.Abp.Threading.xml", "lib/netstandard2.1/Volo.Abp.Threading.dll", "lib/netstandard2.1/Volo.Abp.Threading.pdb", "lib/netstandard2.1/Volo.Abp.Threading.xml", "volo.abp.threading.8.1.1.nupkg.sha512", "volo.abp.threading.nuspec"]}, "Volo.Abp.Timing/8.1.1": {"sha512": "+qmruseshbQ2utykpTu/czIAs9kWWS6FMWkV2/nw98/LYMppveX3Wz7TO9t6QsDCWPXOgvtAOU2xd0IB/aiOlw==", "type": "package", "path": "volo.abp.timing/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Timing.abppkg", "content/Volo.Abp.Timing.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Timing.dll", "lib/net8.0/Volo.Abp.Timing.pdb", "lib/net8.0/Volo.Abp.Timing.xml", "lib/netstandard2.0/Volo.Abp.Timing.dll", "lib/netstandard2.0/Volo.Abp.Timing.pdb", "lib/netstandard2.0/Volo.Abp.Timing.xml", "lib/netstandard2.1/Volo.Abp.Timing.dll", "lib/netstandard2.1/Volo.Abp.Timing.pdb", "lib/netstandard2.1/Volo.Abp.Timing.xml", "volo.abp.timing.8.1.1.nupkg.sha512", "volo.abp.timing.nuspec"]}, "Volo.Abp.Uow/8.1.1": {"sha512": "Jjg0UFya+vJ5omveIuSbXivFqbNuklsUgydpySgRntGMQt4wBxV04ZlqjxzjDi7uCQ8CkRwaxVZNtbIXgdoTbg==", "type": "package", "path": "volo.abp.uow/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Uow.abppkg", "content/Volo.Abp.Uow.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Uow.dll", "lib/net8.0/Volo.Abp.Uow.pdb", "lib/net8.0/Volo.Abp.Uow.xml", "lib/netstandard2.0/Volo.Abp.Uow.dll", "lib/netstandard2.0/Volo.Abp.Uow.pdb", "lib/netstandard2.0/Volo.Abp.Uow.xml", "lib/netstandard2.1/Volo.Abp.Uow.dll", "lib/netstandard2.1/Volo.Abp.Uow.pdb", "lib/netstandard2.1/Volo.Abp.Uow.xml", "volo.abp.uow.8.1.1.nupkg.sha512", "volo.abp.uow.nuspec"]}, "Volo.Abp.Users.Abstractions/8.1.1": {"sha512": "FIRoheeC7EnfDYi6LQfo4Ud50I5T3zJ+UTZqG+7J1akOBaN1FOenAoRwS54SghdRQ3Ti3sIESe2cW79ywc25fg==", "type": "package", "path": "volo.abp.users.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Users.Abstractions.abppkg", "content/Volo.Abp.Users.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Users.Abstractions.dll", "lib/net8.0/Volo.Abp.Users.Abstractions.pdb", "lib/net8.0/Volo.Abp.Users.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Users.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Users.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Users.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Users.Abstractions.xml", "volo.abp.users.abstractions.8.1.1.nupkg.sha512", "volo.abp.users.abstractions.nuspec"]}, "Volo.Abp.Users.Domain.Shared/8.1.1": {"sha512": "s6Kcg36g0li48oRfMw/InqimhFCxydSokZJ0r07fwQgUtSWJ0VsHLUpS0ZlyU0e/X9gK3PMLhlEZMGX6AQs36g==", "type": "package", "path": "volo.abp.users.domain.shared/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Users.Domain.Shared.abppkg", "content/Volo.Abp.Users.Domain.Shared.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Users.Domain.Shared.dll", "lib/net8.0/Volo.Abp.Users.Domain.Shared.pdb", "lib/net8.0/Volo.Abp.Users.Domain.Shared.xml", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.Users.Domain.Shared.xml", "lib/netstandard2.1/Volo.Abp.Users.Domain.Shared.dll", "lib/netstandard2.1/Volo.Abp.Users.Domain.Shared.pdb", "lib/netstandard2.1/Volo.Abp.Users.Domain.Shared.xml", "volo.abp.users.domain.shared.8.1.1.nupkg.sha512", "volo.abp.users.domain.shared.nuspec"]}, "Volo.Abp.Validation/8.1.1": {"sha512": "ecqXxmkppJxkZ+NkSAqSgDxCoQ5UeQg3AlFJDwb4W13WRHqsS5R4uvvxMvsYdAz87uxOjAieZm+KE3dFtBX6hQ==", "type": "package", "path": "volo.abp.validation/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Validation.abppkg", "content/Volo.Abp.Validation.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Validation.dll", "lib/net8.0/Volo.Abp.Validation.pdb", "lib/net8.0/Volo.Abp.Validation.xml", "lib/netstandard2.0/Volo.Abp.Validation.dll", "lib/netstandard2.0/Volo.Abp.Validation.pdb", "lib/netstandard2.0/Volo.Abp.Validation.xml", "lib/netstandard2.1/Volo.Abp.Validation.dll", "lib/netstandard2.1/Volo.Abp.Validation.pdb", "lib/netstandard2.1/Volo.Abp.Validation.xml", "volo.abp.validation.8.1.1.nupkg.sha512", "volo.abp.validation.nuspec"]}, "Volo.Abp.Validation.Abstractions/8.1.1": {"sha512": "lyYP+IY6MGvrCOXM5DPZ3GI4iH3Xc+oXYPzi6kmRrFov8NSYUxCLYfnE2eNC3MqFC2Dw2JUmtumaVJ0g+t/OBQ==", "type": "package", "path": "volo.abp.validation.abstractions/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Validation.Abstractions.abppkg", "content/Volo.Abp.Validation.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Validation.Abstractions.dll", "lib/net8.0/Volo.Abp.Validation.Abstractions.pdb", "lib/net8.0/Volo.Abp.Validation.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.xml", "volo.abp.validation.abstractions.8.1.1.nupkg.sha512", "volo.abp.validation.abstractions.nuspec"]}, "Volo.Abp.VirtualFileSystem/8.1.1": {"sha512": "K0kwo4iaxVGro7Av3+wot+oIMqzwcC+xRjCODjznTGMH5d2Mao9WMSonQK3sphSkH7q5kukyKwS1VwS+V0Pg1w==", "type": "package", "path": "volo.abp.virtualfilesystem/8.1.1", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.VirtualFileSystem.abppkg", "content/Volo.Abp.VirtualFileSystem.abppkg.analyze.json", "lib/net8.0/Volo.Abp.VirtualFileSystem.dll", "lib/net8.0/Volo.Abp.VirtualFileSystem.pdb", "lib/net8.0/Volo.Abp.VirtualFileSystem.xml", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.xml", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.xml", "volo.abp.virtualfilesystem.8.1.1.nupkg.sha512", "volo.abp.virtualfilesystem.nuspec"]}, "EasyAbp.AbpHelper.Gui.Application.Contracts/2.16.0": {"type": "project", "path": "../../src/EasyAbp.AbpHelper.Gui.Application.Contracts/EasyAbp.AbpHelper.Gui.Application.Contracts.csproj", "msbuildProject": "../../src/EasyAbp.AbpHelper.Gui.Application.Contracts/EasyAbp.AbpHelper.Gui.Application.Contracts.csproj"}, "EasyAbp.AbpHelper.Gui.HttpApi.Client/2.16.0": {"type": "project", "path": "../../src/EasyAbp.AbpHelper.Gui.HttpApi.Client/EasyAbp.AbpHelper.Gui.HttpApi.Client.csproj", "msbuildProject": "../../src/EasyAbp.AbpHelper.Gui.HttpApi.Client/EasyAbp.AbpHelper.Gui.HttpApi.Client.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["EasyAbp.AbpHelper.Gui.HttpApi.Client >= 2.16.0", "Microsoft.Extensions.Hosting >= 8.0.1", "Microsoft.Extensions.Http.Polly >= 8.0.10", "Volo.Abp.Http.Client.IdentityModel >= 8.1.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp\\EasyAbp.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp.csproj", "projectName": "EasyAbp.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp\\EasyAbp.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.HttpApi.Client.ConsoleTestApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.HttpApi.Client\\EasyAbp.AbpHelper.Gui.HttpApi.Client.csproj": {"projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.HttpApi.Client\\EasyAbp.AbpHelper.Gui.HttpApi.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.10, )"}, "Volo.Abp.Http.Client.IdentityModel": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}