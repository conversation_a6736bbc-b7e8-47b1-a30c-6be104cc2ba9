﻿@using AbpTools.AbpHelper.Gui.Solutions.Dtos
@inherits AbpTools.AbpHelper.Gui.Blazor.Pages.Solutions.Shared.SolutionManagementBase
<BarItem>
    <BarDropdown RightAligned>
        <BarDropdownToggle>
            @(CurrentSolution.Value?.DisplayName ?? L["NoSolution"])
        </BarDropdownToggle>
        <BarDropdownMenu>
            @foreach (var solution in Solutions)
            {
                <BarDropdownItem Clicked="() => ChangeSolutionAsync(solution)">
                    @solution.DisplayName @if (CurrentSolution.Value.Equals(solution))
                                          {
                                              @($" ({L["Current"].Value})")
                                          }
                </BarDropdownItem>
            }
            <BarDropdownItem Clicked="OpenOpenSolutionModalAsync">
                <Icon Name="IconName.FolderOpen"/> @L["OpenSolution"]
            </BarDropdownItem>
            <BarDropdownItem Clicked="RedirectToSolutionsPageAsync">
                <Icon Name="IconName.List"/> @L["ManageSolutions"]
            </BarDropdownItem>
        </BarDropdownMenu>
    </BarDropdown>
</BarItem>

<Modal @ref="Modal" Style="overflow: visible">
    <ModalContent Size="ModalSize.Default" IsCentered="true">
        <ModalHeader>
            <ModalTitle>@L["OpenSolution"]</ModalTitle>
            <CloseButton Clicked="CloseOpenSolutionModal" />
        </ModalHeader>
        <ModalBody MaxHeight="50">
            <Validations @ref="ValidationsRef" Model="CreateSolution" ValidateOnLoad="false">
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["SolutionDisplayName"]</FieldLabel>
                        <TextEdit @bind-Text="@CreateSolution.DisplayName">
                            <Feedback>
                                <ValidationError />
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Field>
                    <FieldLabel>@L["SolutionSolutionType"]</FieldLabel>
                    <Select TValue="SolutionType" @bind-SelectedValue="@CreateSolution.SolutionType">
                        @foreach (var solutionType in Enum.GetValues<SolutionType>())
                        {
                            <SelectItem Value="solutionType">
                                @L[$"SolutionType:{Enum.GetName(solutionType)}"]
                            </SelectItem>
                        }
                    </Select>
                </Field>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["SolutionDirectoryPath"]</FieldLabel>
                        <TextEdit @bind-Text="@CreateSolution.DirectoryPath">
                            <Feedback>
                                <ValidationError />
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
            </Validations>
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="CloseOpenSolutionModal">@L["Cancel"]</Button>
            <Button Color="Color.Primary" Clicked="OpenSolutionExecuteAsync">@L["Open"]</Button>
        </ModalFooter>
    </ModalContent>
</Modal>