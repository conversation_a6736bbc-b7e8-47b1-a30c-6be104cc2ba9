﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">False</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.aspnetcore.serilog\8.1.1\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Serilog.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.aspnetcore.serilog\8.1.1\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Serilog.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.AspNetCore.Serilog</NuGetPackageId>
      <NuGetPackageVersion>8.1.1</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.AspNetCore.Serilog.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)volo.abp.aspnetcore.mvc.ui.theme.basic\8.1.1\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic.abppkg.analyze.json" Condition="Exists('$(NuGetPackageRoot)volo.abp.aspnetcore.mvc.ui.theme.basic\8.1.1\contentFiles\any\net8.0\Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic.abppkg.analyze.json')">
      <NuGetPackageId>Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic</NuGetPackageId>
      <NuGetPackageVersion>8.1.1</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic.abppkg.analyze.json</Link>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)blazorx-analytics\1.0.0\contentFiles\any\netstandard2.0\wwwroot\blazor-analytics.js" Condition="Exists('$(NuGetPackageRoot)blazorx-analytics\1.0.0\contentFiles\any\netstandard2.0\wwwroot\blazor-analytics.js')">
      <NuGetPackageId>Blazorx-Analytics</NuGetPackageId>
      <NuGetPackageVersion>1.0.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <Private>False</Private>
      <Link>wwwroot\blazor-analytics.js</Link>
    </Content>
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.props" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\8.0.0\buildTransitive\net6.0\Microsoft.Extensions.Configuration.UserSecrets.props')" />
    <Import Project="$(NuGetPackageRoot)volo.abp.aspnetcore.components.web\8.1.1\buildTransitive\Volo.Abp.AspNetCore.Components.Web.props" Condition="Exists('$(NuGetPackageRoot)volo.abp.aspnetcore.components.web\8.1.1\buildTransitive\Volo.Abp.AspNetCore.Components.Web.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise\1.6.2\buildTransitive\Blazorise.props" Condition="Exists('$(NuGetPackageRoot)blazorise\1.6.2\buildTransitive\Blazorise.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise.snackbar\1.4.1\buildTransitive\Blazorise.Snackbar.props" Condition="Exists('$(NuGetPackageRoot)blazorise.snackbar\1.4.1\buildTransitive\Blazorise.Snackbar.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise.datagrid\1.4.1\buildTransitive\Blazorise.DataGrid.props" Condition="Exists('$(NuGetPackageRoot)blazorise.datagrid\1.4.1\buildTransitive\Blazorise.DataGrid.props')" />
    <Import Project="$(NuGetPackageRoot)volo.abp.blazoriseui\8.1.1\buildTransitive\Volo.Abp.BlazoriseUI.props" Condition="Exists('$(NuGetPackageRoot)volo.abp.blazoriseui\8.1.1\buildTransitive\Volo.Abp.BlazoriseUI.props')" />
    <Import Project="$(NuGetPackageRoot)blazorx-analytics\1.0.0\buildTransitive\Blazorx-Analytics.props" Condition="Exists('$(NuGetPackageRoot)blazorx-analytics\1.0.0\buildTransitive\Blazorx-Analytics.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise.icons.fontawesome\1.6.2\buildTransitive\Blazorise.Icons.FontAwesome.props" Condition="Exists('$(NuGetPackageRoot)blazorise.icons.fontawesome\1.6.2\buildTransitive\Blazorise.Icons.FontAwesome.props')" />
    <Import Project="$(NuGetPackageRoot)blazorise.bootstrap5\1.6.2\buildTransitive\Blazorise.Bootstrap5.props" Condition="Exists('$(NuGetPackageRoot)blazorise.bootstrap5\1.6.2\buildTransitive\Blazorise.Bootstrap5.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.3</PkgMicrosoft_CodeAnalysis_Analyzers>
    <PkgMicrosoft_Extensions_ApiDescription_Server Condition=" '$(PkgMicrosoft_Extensions_ApiDescription_Server)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.extensions.apidescription.server\6.0.5</PkgMicrosoft_Extensions_ApiDescription_Server>
  </PropertyGroup>
</Project>