<Project Sdk="Microsoft.NET.Sdk.Web">
    <Import Project="..\..\common.props" />
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
        <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
        <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
        <MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
        <PreserveCompilationReferences>true</PreserveCompilationReferences>
        <UserSecretsId>AbpTools.AbpHelper.Gui-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Blazorx-Analytics"
                          Version="1.0.0" />
        <PackageReference Include="Blazorise.Bootstrap5"
                          Version="$(BlazoriseVersion)" />
        <PackageReference Include="Blazorise.Icons.FontAwesome"
                          Version="$(BlazoriseVersion)" />
        <PackageReference Include="Serilog.AspNetCore"
                          Version="8.0.2" />
        <PackageReference Include="Serilog.Sinks.Async"
                          Version="2.0.0" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\AbpTools.AbpHelper.Gui.Application\AbpTools.AbpHelper.Gui.Application.csproj" />
        <ProjectReference Include="..\AbpTools.AbpHelper.Gui.HttpApi\AbpTools.AbpHelper.Gui.HttpApi.csproj" />
        <PackageReference Include="Volo.Abp.AspNetCore.Components.Server.LeptonXLiteTheme"
                          Version="$(AbpLeptonXThemeVersion)" />
        <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.Basic"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Autofac"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Swashbuckle"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.AspNetCore.Serilog"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.SettingManagement.Blazor.Server"
                          Version="$(AbpVersion)" />
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="Logs\**" />
        <Content Remove="Logs\**" />
        <EmbeddedResource Remove="Logs\**" />
        <None Remove="Logs\**" />
    </ItemGroup>
    <ItemGroup>
        <None Update="Pages\**\*.js">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Pages\**\*.css">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>
</Project>