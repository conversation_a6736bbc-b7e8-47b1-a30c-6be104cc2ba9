﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.New;
using AbpTools.AbpHelper.Gui.AbpCli.New.Dtos;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.New
{
    public partial class CreateConsole
    {
        [Inject]
        private IAbpCliNewAppService Service { get; set; }
        
        protected AbpNewConsoleInput Input { get; set; } = new()
        {
            DatabaseManagementSystem = Database.SqlServer,
            Version = AbpVersionHelper.AbpVersion
        };

        protected override async Task InternalExecuteAsync()
        {
            await Service.CreateConsoleAsync(Input);
        }
    }
}
