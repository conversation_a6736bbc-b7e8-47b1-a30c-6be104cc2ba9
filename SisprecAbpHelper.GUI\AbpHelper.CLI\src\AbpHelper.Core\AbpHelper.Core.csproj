<Project Sdk="Microsoft.NET.Sdk">
    <Import Project="..\..\common.props" />
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <PackageId>AbpTools.AbpHelper.Core</PackageId>
        <AssemblyName>AbpTools.AbpHelper.Core</AssemblyName>
        <RootNamespace>AbpTools.AbpHelper.Core</RootNamespace>
        <LangVersion>10.0</LangVersion>
        <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Bogus"
                          Version="35.5.1" />
        <PackageReference Include="Elsa"
                          Version="1.2.2.29" />
        <PackageReference Include="Humanizer.Core"
                          Version="2.14.1" />
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp"
                          Version="4.5.0" />
        <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded"
                          Version="8.0.0" />
        <PackageReference Include="Scriban"
                          Version="5.10.0" />
        <PackageReference Include="System.CommandLine"
                          Version="2.0.0-beta1.20371.2" />
        <PackageReference Include="Volo.Abp.Autofac"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Core"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Http"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.VirtualFileSystem"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Serilog.Extensions.Logging"
                          Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.File"
                          Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.Console"
                          Version="6.0.0" />
        <PackageReference Include="NuGet.Protocol"
                          Version="6.2.2" />
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="Templates\**\*.*" />
        <Content Remove="Templates\**\*.*" />
        <EmbeddedResource Include="Templates\**\*.*" />
    </ItemGroup>
</Project>