﻿@using AbpTools.AbpHelper.Gui.Blazor.Pages.Shared;
@inherits ExecutableComponentBase

<Modal @ref="_actuatorModal">
    <ModalContent Size="ModalSize.Large" IsCentered="true">
        <ModalHeader>
            <ModalTitle>@L["InstallationActuator"]</ModalTitle>
            <CloseButton Clicked="CloseModal" />
        </ModalHeader>
        <ModalBody MaxHeight="50">
            
                @foreach (var addManyModuleInput in AddInputList)
                {
                    @foreach (var installationInfo in addManyModuleInput.InstallationInfos)
                    {
                        <Field>
                            <span style="color: #008000"><b>[@(L["InstallationActuator:Add"])]&nbsp;</b></span>
                            <span><b>@installationInfo.PackageName</b></span>
                            @if (addManyModuleInput.SpecifiedVersion != null)
                            {
                                <span>&nbsp;@($"(v{addManyModuleInput.SpecifiedVersion})")</span>
                            }
                            <span>&nbsp;@L["InstallationActuator:To"]&nbsp;<b>@installationInfo.Targets.JoinAsString(", ")</b></span>
                            <Blazorise.Link Clicked="() => OpenEditTargetModalAsync(installationInfo)"><Icon Name="IconName.Edit"></Icon></Blazorise.Link>
                        </Field>
                    }
                }
                
                @foreach (var removeManyModuleInput in RemoveInputList)
                {
                    @foreach (var installationInfo in removeManyModuleInput.InstallationInfos)
                    {
                        <Field>
                            <span style="color: #ff0000"><b>[@(L["InstallationActuator:Remove"])]&nbsp;</b></span>
                            <span><b>@installationInfo.PackageName</b></span>
                            <span>&nbsp;@L["InstallationActuator:From"]&nbsp;<b>@installationInfo.Targets.JoinAsString(", ")</b></span>
                            <Blazorise.Link Clicked="() => OpenEditTargetModalAsync(installationInfo)"><Icon Name="IconName.Edit"></Icon></Blazorise.Link>
                        </Field>
                    }
                }
            
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="CloseModal">@L["Cancel"]</Button>
            <Button Color="Color.Primary" Clicked="ExecuteAsync" Disabled="@Executing" Loading="@Executing">@L["Execute"]</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

<Modal @ref="_editTargetModal">
    <ModalContent Size="ModalSize.Small" IsCentered="true">
        <ModalHeader>
            <ModalTitle>@L["InstallationTarget"]</ModalTitle>
            <CloseButton Clicked="CloseEditTargetModal" />
        </ModalHeader>
        <ModalBody MaxHeight="30">
            <Field>
                <FieldLabel>@L["InstallationTarget"]</FieldLabel>
                <TextEdit @bind-Text="EditingTargetString" Placeholder="e.g. Application,HttpApi.Client,Web"></TextEdit>
            </Field>
            @_editTargetModal.Attributes
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="CloseEditTargetModal">@L["Cancel"]</Button>
            <Button Color="Color.Primary" Clicked="SaveTargetAsync">@L["Save"]</Button>
        </ModalFooter>
    </ModalContent>
</Modal>