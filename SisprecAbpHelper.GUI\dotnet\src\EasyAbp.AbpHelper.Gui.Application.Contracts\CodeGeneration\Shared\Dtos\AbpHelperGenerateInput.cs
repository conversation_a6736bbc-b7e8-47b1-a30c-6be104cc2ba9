﻿using System;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using JetBrains.Annotations;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Shared.Dtos
{
    [Serializable]
    public abstract class AbpHelperGenerateInput : AbpHelperInput
    {
        public virtual bool NoOverwrite { get; set; }

        public AbpHelperGenerateInput()
        {
        }

        public AbpHelperGenerateInput([NotNull] string directory, [CanBeNull] string projectName,
            [CanBeNull] string exclude, bool noOverwrite) :
            base(directory, projectName, exclude)
        {
        }
    }
}