﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Add.Dtos;
using AbpTools.AbpHelper.Gui.Common;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Cli.Commands;

namespace AbpTools.AbpHelper.Gui.AbpCli.Add
{
    public class AbpCliAddAppService : AbpCliAppService, IAbpCliAddAppService
    {
        private readonly AddPackageCommand _addPackageCommand;
        private readonly AddModuleCommand _addModuleCommand;
        private readonly ICurrentDirectoryHelper _currentDirectoryHelper;

        public AbpCliAddAppService(
            AddPackageCommand addPackageCommand,
            AddModuleCommand addModuleCommand,
            ICurrentDirectoryHelper currentDirectoryHelper)
        {
            _addPackageCommand = addPackageCommand;
            _addModuleCommand = addModuleCommand;
            _currentDirectoryHelper = currentDirectoryHelper;
        }

        public async Task<ServiceExecutionResult> AddPackageAsync(AbpAddPackageInput input)
        {
            var args = CreateCommandLineArgs(input, "abp add-package", input.PackageName);

            using (_currentDirectoryHelper.Change(input.Directory))
            {
                await _addPackageCommand.ExecuteAsync(args);
            }

            return new ServiceExecutionResult(true);
        }

        public async Task<ServiceExecutionResult> AddModuleAsync(AbpAddModuleInput input)
        {
            var args = CreateCommandLineArgs(input, "abp add-module", input.ModuleName);

            using (_currentDirectoryHelper.Change(input.Directory))
            {
                await _addModuleCommand.ExecuteAsync(args);
            }

            return new ServiceExecutionResult(true);
        }
    }
}