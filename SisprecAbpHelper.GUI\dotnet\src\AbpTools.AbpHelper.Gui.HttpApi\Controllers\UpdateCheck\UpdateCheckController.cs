﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.Solutions;
using AbpTools.AbpHelper.Gui.Solutions.Dtos;
using AbpTools.AbpHelper.Gui.UpdateCheck;
using AbpTools.AbpHelper.Gui.UpdateCheck.Dtos;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.Application.Dtos;

namespace AbpTools.AbpHelper.Gui.Controllers.UpdateCheck
{
    [RemoteService]
    [Route("/api/abp-helper/update-check")]
    public class UpdateCheckController : GuiController, IUpdateCheckAppService
    {
        private readonly IUpdateCheckAppService _service;

        public UpdateCheckController(IUpdateCheckAppService service)
        {
            _service = service;
        }

        [HttpPost]
        public Task<UpdateCheckOutput> CheckAsync()
        {
            return _service.CheckAsync();
        }
    }
}