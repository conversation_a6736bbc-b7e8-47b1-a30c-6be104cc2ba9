{"format": 1, "restore": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\AbpTools.AbpHelper.Gui.TestBase\\AbpTools.AbpHelper.Gui.TestBase.csproj": {}}, "projects": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\AbpTools.AbpHelper.Gui.TestBase\\AbpTools.AbpHelper.Gui.TestBase.csproj": {"version": "2.16.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\AbpTools.AbpHelper.Gui.TestBase\\AbpTools.AbpHelper.Gui.TestBase.csproj", "projectName": "AbpTools.AbpHelper.Gui.TestBase", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\AbpTools.AbpHelper.Gui.TestBase\\AbpTools.AbpHelper.Gui.TestBase.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\AbpTools.AbpHelper.Gui.TestBase\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )"}, "NSubstitute": {"target": "Package", "version": "[5.1.0, )"}, "Shouldly": {"target": "Package", "version": "[4.2.1, )"}, "Volo.Abp.Authorization": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.TestBase": {"target": "Package", "version": "[8.1.1, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.extensibility.execution": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}