[{"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliAddController", "Method": "AddModuleAsync", "RelativePath": "api/abp-helper/abp-cli/add/module", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Add.Dtos.AbpAddModuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliAddController", "Method": "AddPackageAsync", "RelativePath": "api/abp-helper/abp-cli/add/package", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Add.Dtos.AbpAddPackageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliInstallLibsController", "Method": "InstallLibsAsync", "RelativePath": "api/abp-helper/abp-cli/build", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.InstallLibs.Dtos.AbpInstallLibsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliBundleController", "Method": "RunAsync", "RelativePath": "api/abp-helper/abp-cli/bundle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Bundle.Dtos.AbpBundleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliGetSourceController", "Method": "GetSourceAsync", "RelativePath": "api/abp-helper/abp-cli/get-source", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.GetSource.Dtos.AbpGetSourceInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliLoginController", "Method": "LoginAsync", "RelativePath": "api/abp-helper/abp-cli/login/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Login.Dtos.AbpLoginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliLoginController", "Method": "LogoutAsync", "RelativePath": "api/abp-helper/abp-cli/login/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliNewController", "Method": "CreateAppAsync", "RelativePath": "api/abp-helper/abp-cli/new/app", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewAppInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliNewController", "Method": "CreateAppNoLayersAsync", "RelativePath": "api/abp-helper/abp-cli/new/app-nolayers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewAppNoLayersInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliNewController", "Method": "CreateConsoleAsync", "RelativePath": "api/abp-helper/abp-cli/new/console", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewConsoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliNewController", "Method": "CreateMauiAsync", "RelativePath": "api/abp-helper/abp-cli/new/maui", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewMauiInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliNewController", "Method": "CreateModuleAsync", "RelativePath": "api/abp-helper/abp-cli/new/module", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewModuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliProxyController", "Method": "GenerateCSharpProxyAsync", "RelativePath": "api/abp-helper/abp-cli/proxy/generate/csharp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveCSharpProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliProxyController", "Method": "GenerateJavaScriptProxyAsync", "RelativePath": "api/abp-helper/abp-cli/proxy/generate/js", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveJavaScriptProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliProxyController", "Method": "GenerateAngularProxyAsync", "RelativePath": "api/abp-helper/abp-cli/proxy/generate/ng", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveAngularProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliProxyController", "Method": "RemoveCSharpProxyAsync", "RelativePath": "api/abp-helper/abp-cli/proxy/remove/csharp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveCSharpProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliProxyController", "Method": "RemoveJavaScriptProxyAsync", "RelativePath": "api/abp-helper/abp-cli/proxy/remove/js", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveJavaScriptProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliProxyController", "Method": "RemoveAngularProxyAsync", "RelativePath": "api/abp-helper/abp-cli/proxy/remove/ng", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveAngularProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliSwitchController", "Method": "SwitchToLocalAsync", "RelativePath": "api/abp-helper/abp-cli/switch/to-local", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToLocalInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliSwitchController", "Method": "SwitchToNightlyAsync", "RelativePath": "api/abp-helper/abp-cli/switch/to-nightly", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToNightlyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliSwitchController", "Method": "SwitchToPreRcAsync", "RelativePath": "api/abp-helper/abp-cli/switch/to-prerc", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToPreRcInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliSwitchController", "Method": "SwitchToPreviewAsync", "RelativePath": "api/abp-helper/abp-cli/switch/to-preview", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToPreviewInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliSwitchController", "Method": "SwitchToStableAsync", "RelativePath": "api/abp-helper/abp-cli/switch/to-stable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToStableInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliTranslateController", "Method": "ApplyChangesAsync", "RelativePath": "api/abp-helper/abp-cli/translate/apply-changes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Translate.Dtos.AbpApplyChangesInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliTranslateController", "Method": "CreateTranslationFileAsync", "RelativePath": "api/abp-helper/abp-cli/translate/create-translation-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Translate.Dtos.AbpCreateTranslationFileInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.AbpCli.AbpCliUpdateController", "Method": "UpdateAsync", "RelativePath": "api/abp-helper/abp-cli/update/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Update.Dtos.AbpUpdateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.CodeGeneration.CodeGenerationAppServiceController", "Method": "GenerateClassAsync", "RelativePath": "api/abp-helper/code-generation/app-service/class", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos.AbpHelperGenerateAppServiceClassInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.CodeGeneration.CodeGenerationAppServiceController", "Method": "GenerateMethodsAsync", "RelativePath": "api/abp-helper/code-generation/app-service/methods", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos.AbpHelperGenerateAppServiceMethodsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.CodeGeneration.CodeGenerationControllerController", "Method": "GenerateAsync", "RelativePath": "api/abp-helper/code-generation/controller", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Controller.Dtos.AbpHelperGenerateControllerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.CodeGeneration.CodeGenerationCrudController", "Method": "GenerateAsync", "RelativePath": "api/abp-helper/code-generation/crud", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Crud.Dtos.AbpHelperGenerateCrudInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.CodeGeneration.CodeGenerationLocalizationController", "Method": "GenerateItemsAsync", "RelativePath": "api/abp-helper/code-generation/localization/items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Localization.Dtos.AbpHelperGenerateLocalizationItemsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.CodeGeneration.CodeGenerationMigrationController", "Method": "AddAsync", "RelativePath": "api/abp-helper/code-generation/migration/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Migration.Dtos.AbpHelperGenerateMigrationAddInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.CodeGeneration.CodeGenerationMigrationController", "Method": "RemoveAsync", "RelativePath": "api/abp-helper/code-generation/migration/remove", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Migration.Dtos.AbpHelperGenerateMigrationRemoveInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.LogService.LogController", "Method": "GetRecentErrorLogFilePathAsync", "RelativePath": "api/abp-helper/logs/recent-error-log-file-path", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.LogService.LogController", "Method": "GetRecentLogFilePathAsync", "RelativePath": "api/abp-helper/logs/recent-log-file-path", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.ModuleManagement.ModuleManagementExplorerController", "Method": "GetModuleGroupListAsync", "RelativePath": "api/abp-helper/module-management/explorer/module-group", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[AbpTools.AbpHelper.Gui.ModuleManagement.Explorer.Dtos.ModuleGroupDto, AbpTools.AbpHelper.Gui.Application.Contracts, Version=2.16.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.ModuleManagement.ModuleManagementInstallerController", "Method": "AddManyAsync", "RelativePath": "api/abp-helper/module-management/installer/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.ModuleManagement.Installer.Dtos.AddManyModuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.ModuleManagement.ModuleManagementInstallerController", "Method": "RemoveManyAsync", "RelativePath": "api/abp-helper/module-management/installer/remove", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.ModuleManagement.Installer.Dtos.RemoveManyModuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.Solutions.SolutionController", "Method": "GetListAsync", "RelativePath": "api/abp-helper/solutions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[AbpTools.AbpHelper.Gui.Solutions.Dtos.SolutionDto, AbpTools.AbpHelper.Gui.Application.Contracts, Version=2.16.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.Solutions.SolutionController", "Method": "DeleteAsync", "RelativePath": "api/abp-helper/solutions", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "DisplayName", "Type": "System.String", "IsRequired": false}, {"Name": "SolutionType", "Type": "System.String", "IsRequired": false}, {"Name": "DirectoryPath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.Solutions.SolutionController", "Method": "GetPackageDictionaryAsync", "RelativePath": "api/abp-helper/solutions/packages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DirectoryPath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Solutions.Dtos.GetPackageDictionaryOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.Solutions.SolutionController", "Method": "UseAsync", "RelativePath": "api/abp-helper/solutions/use", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.Solutions.Dtos.SolutionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Solutions.Dtos.SolutionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Controllers.UpdateCheck.UpdateCheckController", "Method": "CheckAsync", "RelativePath": "api/abp-helper/update-check", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.UpdateCheck.Dtos.UpdateCheckOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController", "Method": "Get", "RelativePath": "api/abp/api-definition", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "IncludeTypes", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController", "Method": "GetAsync", "RelativePath": "api/abp/application-configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "IncludeLocalizationResources", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController", "Method": "GetAsync", "RelativePath": "api/abp/application-localization", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CultureName", "Type": "System.String", "IsRequired": false}, {"Name": "OnlyDynamics", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Pages.Abp.MultiTenancy.AbpTenantController", "Method": "FindTenantByIdAsync", "RelativePath": "api/abp/multi-tenancy/tenants/by-id/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Pages.Abp.MultiTenancy.AbpTenantController", "Method": "FindTenantByNameAsync", "RelativePath": "api/abp/multi-tenancy/tenants/by-name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.MultiTenancy.FindTenantResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.DynamicClaimsController", "Method": "RefreshAsync", "RelativePath": "api/account/dynamic-claims/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.ProfileController", "Method": "GetAsync", "RelativePath": "api/account/my-profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Volo.Abp.Account.ProfileDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.ProfileController", "Method": "UpdateAsync", "RelativePath": "api/account/my-profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Account.UpdateProfileDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Account.ProfileDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.ProfileController", "Method": "ChangePasswordAsync", "RelativePath": "api/account/my-profile/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Account.ChangePasswordInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.AccountController", "Method": "RegisterAsync", "RelativePath": "api/account/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Account.RegisterDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.AccountController", "Method": "ResetPasswordAsync", "RelativePath": "api/account/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Account.ResetPasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.AccountController", "Method": "SendPasswordResetCodeAsync", "RelativePath": "api/account/send-password-reset-code", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Account.SendPasswordResetCodeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Account.AccountController", "Method": "VerifyPasswordResetTokenAsync", "RelativePath": "api/account/verify-password-reset-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Account.VerifyPasswordResetTokenInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Add.AbpCliAddAppService", "Method": "AddModuleAsync", "RelativePath": "api/app/abp-cli-add/module", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Add.Dtos.AbpAddModuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Add.AbpCliAddAppService", "Method": "AddPackageAsync", "RelativePath": "api/app/abp-cli-add/package", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Add.Dtos.AbpAddPackageInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Bundle.AbpCliBundleAppService", "Method": "RunAsync", "RelativePath": "api/app/abp-cli-bundle/run", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Bundle.Dtos.AbpBundleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Clean.AbpCliCleanAppService", "Method": "CleanAsync", "RelativePath": "api/app/abp-cli-clean/clean", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Clean.Dtos.AbpCleanInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.GetSource.AbpCliGetSourceAppService", "Method": "GetSourceAsync", "RelativePath": "api/app/abp-cli-get-source/source", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ModuleName", "Type": "System.String", "IsRequired": false}, {"Name": "Version", "Type": "System.String", "IsRequired": false}, {"Name": "LocalFrameworkRef", "Type": "System.String", "IsRequired": false}, {"Name": "Preview", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Directory", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.InstallLibs.AbpCliInstallLibsAppService", "Method": "InstallLibsAsync", "RelativePath": "api/app/abp-cli-install-libs/install-libs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.InstallLibs.Dtos.AbpInstallLibsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Login.AbpCliLoginAppService", "Method": "LoginAsync", "RelativePath": "api/app/abp-cli-login/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Login.Dtos.AbpLoginInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Login.AbpCliLoginAppService", "Method": "LogoutAsync", "RelativePath": "api/app/abp-cli-login/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.New.AbpCliNewAppService", "Method": "CreateAppAsync", "RelativePath": "api/app/abp-cli-new/app", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewAppInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.New.AbpCliNewAppService", "Method": "CreateAppNoLayersAsync", "RelativePath": "api/app/abp-cli-new/app-no-layers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewAppNoLayersInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.New.AbpCliNewAppService", "Method": "CreateConsoleAsync", "RelativePath": "api/app/abp-cli-new/console", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewConsoleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.New.AbpCliNewAppService", "Method": "CreateMauiAsync", "RelativePath": "api/app/abp-cli-new/maui", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewMauiInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.New.AbpCliNewAppService", "Method": "CreateModuleAsync", "RelativePath": "api/app/abp-cli-new/module", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.New.Dtos.AbpNewModuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.AbpCliProxyAppService", "Method": "RemoveAngularProxyAsync", "RelativePath": "api/app/abp-cli-proxy/angular-proxy", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "ApiName", "Type": "System.String", "IsRequired": false}, {"Name": "Source", "Type": "System.String", "IsRequired": false}, {"Name": "Target", "Type": "System.String", "IsRequired": false}, {"Name": "EntryPoint", "Type": "System.String", "IsRequired": false}, {"Name": "Url", "Type": "System.String", "IsRequired": false}, {"Name": "ServiceType", "Type": "System.String", "IsRequired": false}, {"Name": "Directory", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.AbpCliProxyAppService", "Method": "RemoveCSharpProxyAsync", "RelativePath": "api/app/abp-cli-proxy/c-sharp-proxy", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "Url", "Type": "System.String", "IsRequired": false}, {"Name": "ServiceType", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "WithoutContracts", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Folder", "Type": "System.String", "IsRequired": false}, {"Name": "Directory", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.AbpCliProxyAppService", "Method": "GenerateAngularProxyAsync", "RelativePath": "api/app/abp-cli-proxy/generate-angular-proxy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveAngularProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.AbpCliProxyAppService", "Method": "GenerateCSharpProxyAsync", "RelativePath": "api/app/abp-cli-proxy/generate-cSharp-proxy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveCSharpProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.AbpCliProxyAppService", "Method": "GenerateJavaScriptProxyAsync", "RelativePath": "api/app/abp-cli-proxy/generate-java-script-proxy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.Dtos.AbpGenerateRemoveJavaScriptProxyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Proxy.AbpCliProxyAppService", "Method": "RemoveJavaScriptProxyAsync", "RelativePath": "api/app/abp-cli-proxy/java-script-proxy", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "Url", "Type": "System.String", "IsRequired": false}, {"Name": "ServiceType", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Output", "Type": "System.String", "IsRequired": false}, {"Name": "Directory", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Switch.AbpCliSwitchAppService", "Method": "SwitchToLocalAsync", "RelativePath": "api/app/abp-cli-switch/switch-to-local", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToLocalInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Switch.AbpCliSwitchAppService", "Method": "SwitchToNightlyAsync", "RelativePath": "api/app/abp-cli-switch/switch-to-nightly", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToNightlyInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Switch.AbpCliSwitchAppService", "Method": "SwitchToPreRcAsync", "RelativePath": "api/app/abp-cli-switch/switch-to-pre-rc", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToPreRcInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Switch.AbpCliSwitchAppService", "Method": "SwitchToPreviewAsync", "RelativePath": "api/app/abp-cli-switch/switch-to-preview", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToPreviewInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Switch.AbpCliSwitchAppService", "Method": "SwitchToStableAsync", "RelativePath": "api/app/abp-cli-switch/switch-to-stable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Switch.Dtos.AbpSwitchToStableInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Translate.AbpCliTranslateAppService", "Method": "ApplyChangesAsync", "RelativePath": "api/app/abp-cli-translate/apply-changes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Translate.Dtos.AbpApplyChangesInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Translate.AbpCliTranslateAppService", "Method": "CreateTranslationFileAsync", "RelativePath": "api/app/abp-cli-translate/translation-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Translate.Dtos.AbpCreateTranslationFileInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.AbpCli.Update.AbpCliUpdateAppService", "Method": "UpdateAsync", "RelativePath": "api/app/abp-cli-update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.AbpCli.Update.Dtos.AbpUpdateInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.CodeGeneration.AppService.CodeGenerationAppServiceAppService", "Method": "GenerateClassAsync", "RelativePath": "api/app/code-generation-app-service/generate-class", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos.AbpHelperGenerateAppServiceClassInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.CodeGeneration.AppService.CodeGenerationAppServiceAppService", "Method": "GenerateMethodsAsync", "RelativePath": "api/app/code-generation-app-service/generate-methods", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos.AbpHelperGenerateAppServiceMethodsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.CodeGeneration.Controller.CodeGenerationControllerAppService", "Method": "GenerateAsync", "RelativePath": "api/app/code-generation-controller/generate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Controller.Dtos.AbpHelperGenerateControllerInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.CodeGeneration.Crud.CodeGenerationCrudAppService", "Method": "GenerateAsync", "RelativePath": "api/app/code-generation-crud/generate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Crud.Dtos.AbpHelperGenerateCrudInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.CodeGeneration.Localization.CodeGenerationLocalizationAppService", "Method": "GenerateItemsAsync", "RelativePath": "api/app/code-generation-localization/generate-items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Localization.Dtos.AbpHelperGenerateLocalizationItemsInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.CodeGeneration.Migration.CodeGenerationMigrationAppService", "Method": "AddAsync", "RelativePath": "api/app/code-generation-migration", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.CodeGeneration.Migration.Dtos.AbpHelperGenerateMigrationAddInput", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.CodeGeneration.Migration.CodeGenerationMigrationAppService", "Method": "RemoveAsync", "RelativePath": "api/app/code-generation-migration", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "EfOptions", "Type": "System.String", "IsRequired": false}, {"Name": "MigrationProjectName", "Type": "System.String", "IsRequired": false}, {"Name": "Exclude", "Type": "System.String", "IsRequired": false}, {"Name": "ProjectName", "Type": "System.String", "IsRequired": false}, {"Name": "Directory", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Shared.Dtos.ServiceExecutionResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.LogService.LogAppService", "Method": "GetRecentErrorLogFilePathAsync", "RelativePath": "api/app/log/recent-error-log-file-path", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.LogService.LogAppService", "Method": "GetRecentLogFilePathAsync", "RelativePath": "api/app/log/recent-log-file-path", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.ModuleManagement.Explorer.ModuleManagementExplorerAppService", "Method": "GetModuleGroupListAsync", "RelativePath": "api/app/module-management-explorer/module-group-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[AbpTools.AbpHelper.Gui.ModuleManagement.Explorer.Dtos.ModuleGroupDto, AbpTools.AbpHelper.Gui.Application.Contracts, Version=2.16.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.ModuleManagement.Installer.ModuleManagementInstallerAppService", "Method": "AddManyAsync", "RelativePath": "api/app/module-management-installer/many", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.ModuleManagement.Installer.Dtos.AddManyModuleInput", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.ModuleManagement.Installer.ModuleManagementInstallerAppService", "Method": "RemoveManyAsync", "RelativePath": "api/app/module-management-installer/many", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "DirectoryPath", "Type": "System.String", "IsRequired": false}, {"Name": "InstallationInfos", "Type": "System.Collections.Generic.List`1[[AbpTools.AbpHelper.Gui.ModuleManagement.Installer.Dtos.InstallationInfo, AbpTools.AbpHelper.Gui.Application.Contracts, Version=2.16.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Solutions.SolutionAppService", "Method": "GetListAsync", "RelativePath": "api/app/solution", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[AbpTools.AbpHelper.Gui.Solutions.Dtos.SolutionDto, AbpTools.AbpHelper.Gui.Application.Contracts, Version=2.16.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Solutions.SolutionAppService", "Method": "DeleteAsync", "RelativePath": "api/app/solution", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "DisplayName", "Type": "System.String", "IsRequired": false}, {"Name": "SolutionType", "Type": "System.String", "IsRequired": false}, {"Name": "DirectoryPath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Solutions.SolutionAppService", "Method": "GetPackageDictionaryAsync", "RelativePath": "api/app/solution/package-dictionary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DirectoryPath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Solutions.Dtos.GetPackageDictionaryOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Solutions.SolutionAppService", "Method": "UseAsync", "RelativePath": "api/app/solution/use", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "AbpTools.AbpHelper.Gui.Solutions.Dtos.SolutionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.Solutions.Dtos.SolutionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.UpdateCheck.UpdateCheckAppService", "Method": "CheckAsync", "RelativePath": "api/app/update-check/check", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "AbpTools.AbpHelper.Gui.UpdateCheck.Dtos.UpdateCheckOutput", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityRoleController", "Method": "GetListAsync", "RelativePath": "api/identity/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Filter", "Type": "System.String", "IsRequired": false}, {"Name": "Sorting", "Type": "System.String", "IsRequired": false}, {"Name": "SkipCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "MaxResultCount", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.PagedResultDto`1[[Volo.Abp.Identity.IdentityRoleDto, Volo.Abp.Identity.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityRoleController", "Method": "CreateAsync", "RelativePath": "api/identity/roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Identity.IdentityRoleCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityRoleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityRoleController", "Method": "GetAsync", "RelativePath": "api/identity/roles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityRoleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityRoleController", "Method": "UpdateAsync", "RelativePath": "api/identity/roles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "input", "Type": "Volo.Abp.Identity.IdentityRoleUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityRoleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityRoleController", "Method": "DeleteAsync", "RelativePath": "api/identity/roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityRoleController", "Method": "GetAllListAsync", "RelativePath": "api/identity/roles/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[Volo.Abp.Identity.IdentityRoleDto, Volo.Abp.Identity.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "GetListAsync", "RelativePath": "api/identity/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Filter", "Type": "System.String", "IsRequired": false}, {"Name": "Sorting", "Type": "System.String", "IsRequired": false}, {"Name": "SkipCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "MaxResultCount", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.PagedResultDto`1[[Volo.Abp.Identity.IdentityUserDto, Volo.Abp.Identity.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "CreateAsync", "RelativePath": "api/identity/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Volo.Abp.Identity.IdentityUserCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "GetAsync", "RelativePath": "api/identity/users/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "UpdateAsync", "RelativePath": "api/identity/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "input", "Type": "Volo.Abp.Identity.IdentityUserUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "DeleteAsync", "RelativePath": "api/identity/users/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "GetRolesAsync", "RelativePath": "api/identity/users/{id}/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[Volo.Abp.Identity.IdentityRoleDto, Volo.Abp.Identity.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "UpdateRolesAsync", "RelativePath": "api/identity/users/{id}/roles", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "input", "Type": "Volo.Abp.Identity.IdentityUserUpdateRolesDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "GetAssignableRolesAsync", "RelativePath": "api/identity/users/assignable-roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[Volo.Abp.Identity.IdentityRoleDto, Volo.Abp.Identity.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "FindByEmailAsync", "RelativePath": "api/identity/users/by-email/{email}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserController", "Method": "FindByUsernameAsync", "RelativePath": "api/identity/users/by-username/{userName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Identity.IdentityUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserLookupController", "Method": "FindByIdAsync", "RelativePath": "api/identity/users/lookup/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Users.UserData", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserLookupController", "Method": "FindByUserNameAsync", "RelativePath": "api/identity/users/lookup/by-username/{userName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Volo.Abp.Users.UserData", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserLookupController", "Method": "GetCountAsync", "RelativePath": "api/identity/users/lookup/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Filter", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Int64", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Identity.IdentityUserLookupController", "Method": "SearchAsync", "RelativePath": "api/identity/users/lookup/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Filter", "Type": "System.String", "IsRequired": false}, {"Name": "Sorting", "Type": "System.String", "IsRequired": false}, {"Name": "SkipCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "MaxResultCount", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.ListResultDto`1[[Volo.Abp.Users.UserData, Volo.Abp.Users.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "AbpTools.AbpHelper.Gui.Blazor.Controllers.LogController", "Method": "GetRecentErrorLogAsync", "RelativePath": "logs/recent-error-log-file", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Elsa.Activities.Http.Controllers.HttpWorkflowsController", "Method": "<PERSON><PERSON>", "RelativePath": "workflows/trigger/{token}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Elsa.Activities.Http.Controllers.HttpWorkflowsController", "Method": "<PERSON><PERSON>", "RelativePath": "workflows/trigger/{token}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}]