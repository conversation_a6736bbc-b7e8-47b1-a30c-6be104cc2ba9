﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.CodeGeneration.Controller;
using AbpTools.AbpHelper.Gui.CodeGeneration.Controller.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;

namespace AbpTools.AbpHelper.Gui.Controllers.CodeGeneration
{
    [RemoteService]
    [Route("/api/abp-helper/code-generation/controller")]
    public class CodeGenerationControllerController : GuiController, ICodeGenerationControllerAppService
    {
        private readonly ICodeGenerationControllerAppService _service;

        public CodeGenerationControllerController(ICodeGenerationControllerAppService service)
        {
            _service = service;
        }
        
        [HttpPost]
        public Task<ServiceExecutionResult> GenerateAsync(AbpHelperGenerateControllerInput input)
        {
            return _service.GenerateAsync(input);
        }
    }
}