﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Bundle;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Bundle
{
    public partial class Bundle
    {
        [Inject]
        private IAbpCliBundleAppService Service { get; set; }

        protected override async Task InternalExecuteAsync()
        {
            await Service.RunAsync(Input);
        }
    }
}
