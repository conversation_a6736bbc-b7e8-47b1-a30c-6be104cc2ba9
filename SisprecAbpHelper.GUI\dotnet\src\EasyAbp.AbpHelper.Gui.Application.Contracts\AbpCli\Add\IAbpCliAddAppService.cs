﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Add.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.AbpCli.Add
{
    public interface IAbpCliAddAppService : IApplicationService
    {
        Task<ServiceExecutionResult> AddPackageAsync(AbpAddPackageInput input);
        
        Task<ServiceExecutionResult> AddModuleAsync(AbpAddModuleInput input);
    }
}