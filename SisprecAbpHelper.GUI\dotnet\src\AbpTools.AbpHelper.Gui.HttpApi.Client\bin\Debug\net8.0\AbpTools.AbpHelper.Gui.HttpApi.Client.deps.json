{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AbpTools.AbpHelper.Gui.HttpApi.Client/2.16.0": {"dependencies": {"AbpTools.AbpHelper.Gui.Application.Contracts": "2.16.0", "Volo.Abp.Account.HttpApi.Client": "8.1.1", "Volo.Abp.Identity.HttpApi.Client": "8.1.1"}, "runtime": {"AbpTools.AbpHelper.Gui.HttpApi.Client.dll": {}}}, "AsyncKeyedLock/6.3.4": {"runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"assemblyVersion": "6.3.4.0", "fileVersion": "6.3.4.0"}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "JetBrains.Annotations/2023.3.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2023.3.0.0"}}}, "Microsoft.AspNetCore.Authorization/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Metadata/8.0.0": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Localization/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "NUglify/1.21.0": {"runtime": {"lib/net5.0/NUglify.dll": {"assemblyVersion": "1.21.0.0", "fileVersion": "1.21.0.0"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Diagnostics.EventLog/6.0.0": {"runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.3.5": {"runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.3.5.0", "fileVersion": "1.3.5.0"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "TimeZoneConverter/6.1.0": {"runtime": {"lib/net6.0/TimeZoneConverter.dll": {"assemblyVersion": "6.1.0.0", "fileVersion": "6.1.0.0"}}}, "Volo.Abp.Account.Application.Contracts/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Identity.Application.Contracts": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Account.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Account.HttpApi.Client/8.1.1": {"dependencies": {"Volo.Abp.Account.Application.Contracts": "8.1.1", "Volo.Abp.Http.Client": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Account.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Auditing.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization/8.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Security": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization.Abstractions/8.1.1": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.0", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BackgroundWorkers/8.1.1": {"dependencies": {"Volo.Abp.Threading": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Castle.Core/8.1.1": {"dependencies": {"Castle.Core": "5.1.1", "Castle.Core.AsyncInterceptor": "2.1.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/8.1.1": {"dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Data/8.1.1": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.ObjectExtending": "8.1.1", "Volo.Abp.Uow": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.1", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.DistributedLocking.Abstractions/8.1.1": {"dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus/8.1.1": {"dependencies": {"Volo.Abp.BackgroundWorkers": "8.1.1", "Volo.Abp.DistributedLocking.Abstractions": "8.1.1", "Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.Guids": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.ObjectExtending": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ExceptionHandling/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Features/8.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Guids/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Http/8.1.1": {"dependencies": {"Volo.Abp.Http.Abstractions": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.Minify": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Http.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Http.Client/8.1.1": {"dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Volo.Abp.Castle.Core": "8.1.1", "Volo.Abp.EventBus": "8.1.1", "Volo.Abp.ExceptionHandling": "8.1.1", "Volo.Abp.Http": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.RemoteServices": "8.1.1", "Volo.Abp.Threading": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Http.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Application.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Authorization": "8.1.1", "Volo.Abp.Identity.Domain.Shared": "8.1.1", "Volo.Abp.PermissionManagement.Application.Contracts": "8.1.1", "Volo.Abp.Users.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Domain.Shared/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Auditing.Contracts": "8.1.1", "Volo.Abp.Features": "8.1.1", "Volo.Abp.Users.Domain.Shared": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.HttpApi.Client/8.1.1": {"dependencies": {"Volo.Abp.Http.Client": "8.1.1", "Volo.Abp.Identity.Application.Contracts": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json/8.1.1": {"dependencies": {"Volo.Abp.Json.SystemTextJson": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.SystemTextJson/8.1.1": {"dependencies": {"System.Text.Json": "8.0.0", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Json.Abstractions": "8.1.1", "Volo.Abp.Timing": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization/8.1.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Settings": "8.1.1", "Volo.Abp.Threading": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Minify/8.1.1": {"dependencies": {"NUglify": "1.21.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Minify.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy/8.1.1": {"dependencies": {"Volo.Abp.Data": "8.1.1", "Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1", "Volo.Abp.Security": "8.1.1", "Volo.Abp.Settings": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Localization": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectExtending/8.1.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Validation.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Application.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Ddd.Application.Contracts": "8.1.1", "Volo.Abp.PermissionManagement.Domain.Shared": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Domain.Shared/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.RemoteServices/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.RemoteServices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Security/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Settings/8.1.1": {"dependencies": {"Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Security": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Threading/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Timing/8.1.1": {"dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.Settings": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Uow/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.EventBus": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Domain.Shared/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation/8.1.1": {"dependencies": {"Volo.Abp.Localization": "8.1.1", "Volo.Abp.Validation.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.VirtualFileSystem/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AbpTools.AbpHelper.Gui.Application.Contracts/2.16.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Account.Application.Contracts": "8.1.1", "Volo.Abp.Identity.Application.Contracts": "8.1.1", "Volo.Abp.ObjectExtending": "8.1.1"}, "runtime": {"AbpTools.AbpHelper.Gui.Application.Contracts.dll": {"assemblyVersion": "2.16.0", "fileVersion": "2.16.0.0"}}}}}, "libraries": {"AbpTools.AbpHelper.Gui.HttpApi.Client/2.16.0": {"type": "project", "serviceable": false, "sha512": ""}, "AsyncKeyedLock/6.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "path": "asynckeyedlock/6.3.4", "hashPath": "asynckeyedlock.6.3.4.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "JetBrains.Annotations/2023.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "path": "jetbrains.annotations/2023.3.0", "hashPath": "jetbrains.annotations.2023.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OGIGJMnlWvQgcweHcv1Mq/P24Zx/brUHeEdD05NzqkSXmQSnFomTvVyCuBtCXT4JPfv2m70y1RSocmd9bIbJRg==", "path": "microsoft.aspnetcore.authorization/8.0.0", "hashPath": "microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OmuSztiZMitRTYlbMNDkBk3BinSsVcOApSNBAsrw+KYNJh6ALarPhWLlKdtvMgrKzpyCY06xtLAjTmQLURHSlQ==", "path": "microsoft.aspnetcore.metadata/8.0.0", "hashPath": "microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Ev1goLIvggLF6uCs6oZvdr9JM+2b1Zj+4FLdBWNW5iw3tm2BymVIb0yMsjnQTBWL7YUmqVWH3u45hSqOfvuqg==", "path": "microsoft.extensions.fileproviders.embedded/9.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "path": "microsoft.extensions.localization/8.0.0", "hashPath": "microsoft.extensions.localization.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "path": "microsoft.extensions.localization.abstractions/8.0.0", "hashPath": "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "NUglify/1.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-9J44o89PRdcS4GOyj0obkLfjCAuJItI4FrNmwALkjRKlzvHVlTB2ALbC9aigIoCMqzy0Xlc0mIVD/jO9WVDHiA==", "path": "nuglify/1.21.0", "hashPath": "nuglify.1.21.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "path": "system.linq.dynamic.core/1.3.5", "hashPath": "system.linq.dynamic.core.1.3.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "TimeZoneConverter/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "path": "timezoneconverter/6.1.0", "hashPath": "timezoneconverter.6.1.0.nupkg.sha512"}, "Volo.Abp.Account.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-9uNJfv9fM//daZoS8eF/XgcTfFn8zYHcpd9fr/TMgfXvHnMDmtKRNRkIga0yuSNa3K4IDvtE501XR2FPOCCIug==", "path": "volo.abp.account.application.contracts/8.1.1", "hashPath": "volo.abp.account.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.Account.HttpApi.Client/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-gbZxtwxH4uQCh3b87JU+MciHSQLO6hgWfPVKH+6/dcGGOpNRK7GGYrvQyYFaShCPANxxws8pcNLGnJ7j1DmY3w==", "path": "volo.abp.account.httpapi.client/8.1.1", "hashPath": "volo.abp.account.httpapi.client.8.1.1.nupkg.sha512"}, "Volo.Abp.Auditing.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-xUk7lOZxcJoQBjPCIiYxZO2D2tavHXpYCIwH3SfG4xOOwb4ro1TqBKCPMki9ermX0yKmaL2eM55wsXT7RRxuLg==", "path": "volo.abp.auditing.contracts/8.1.1", "hashPath": "volo.abp.auditing.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.Authorization/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-O/SsMfTekNmsP6+jP4So1BY3bPyEGDLpwxD4frLAea+/zVg6c2ENldKjAOutTQJX2TcCpD4w7XEZAVhQ+HTd9Q==", "path": "volo.abp.authorization/8.1.1", "hashPath": "volo.abp.authorization.8.1.1.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dBZjyo3xXwn/XFgzIfUk4W8sICOsCKbhk1Ug4n5a8KPG17fcoovFFug4ACcY80TMGsrgQ8TaibZsNRgLXPHnQw==", "path": "volo.abp.authorization.abstractions/8.1.1", "hashPath": "volo.abp.authorization.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.BackgroundWorkers/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-86+vkzHDSKio95VO93bNmpuuRe+usPuQDw+IoRQbwI/4raqFoSyiCGKvGOp/Fnm4pIIutFvqBPpmloexpHdWig==", "path": "volo.abp.backgroundworkers/8.1.1", "hashPath": "volo.abp.backgroundworkers.8.1.1.nupkg.sha512"}, "Volo.Abp.Castle.Core/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-0IRz2H9bWTfl5vNzVxnzUx0tx2q9/YpAgKfX7L5cm5YZSY/S2Cwx5VMjNpVxG9uFf6CtPMMTWWqTF5JyW83YVg==", "path": "volo.abp.castle.core/8.1.1", "hashPath": "volo.abp.castle.core.8.1.1.nupkg.sha512"}, "Volo.Abp.Core/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sNtdDe2/EWSz5tyHHWTJv8s2znVHVEP3019F6vXkoPYeL3kNYjSMX1W+ibH/TP5jmowaRZc9AZbfjHUb2bnHkQ==", "path": "volo.abp.core/8.1.1", "hashPath": "volo.abp.core.8.1.1.nupkg.sha512"}, "Volo.Abp.Data/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-FkGUAlgMFel85sUAVLBSI2K8nt9eEbyZCp9GT8ZVxGLKHnvUNfotefb7MJZSYSpwpmA1Xg8REkd2IQsqsGgzbw==", "path": "volo.abp.data/8.1.1", "hashPath": "volo.abp.data.8.1.1.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-nHy1J2jvP9WxIl5gZBLjQt+oK5DfKVuKDLaLpimiC7LNiRI4vLMyCe1YWJQmSmtMd9QYNpAKCmoBsAB5X1IR4g==", "path": "volo.abp.ddd.application.contracts/8.1.1", "hashPath": "volo.abp.ddd.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.DistributedLocking.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tdnb/kPLbDmmU5ullLX//3gIBAJ0Et7OuYADydW6a22NHwHsxPhs9w++UEsjkxFKz+0Fy97MkJLOt65xcNjAZA==", "path": "volo.abp.distributedlocking.abstractions/8.1.1", "hashPath": "volo.abp.distributedlocking.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.EventBus/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h/SrkDEK6t54x4A6HnRzNqco+5oB5rf8V8rRJExFDgUxte+OuAEneDcFUm41HtOkIXgDKQiAeYHSDoIe63OnIQ==", "path": "volo.abp.eventbus/8.1.1", "hashPath": "volo.abp.eventbus.8.1.1.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CX13/KxRTEJ9aio/dQd/gtTy00pq8f93p4YNbrmvhAuw9NpWc4rRPuNkRdm7TkpvXX2rezQBzNEZYzanB41KIA==", "path": "volo.abp.eventbus.abstractions/8.1.1", "hashPath": "volo.abp.eventbus.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-A<PERSON>hRYR5THspYRyLFc7lnLPNQkI0Bn4j3tcsXJAiwpQkvPmivLgOL66C9gGBuM42Q9v6Tk6Y6RIy/G70pJbKc9g==", "path": "volo.abp.exceptionhandling/8.1.1", "hashPath": "volo.abp.exceptionhandling.8.1.1.nupkg.sha512"}, "Volo.Abp.Features/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-bMvjYmnxW4NTWwBgGjafSuIBgVdGkwy2xSO0ZIqbcxXTubnnw1C8aiOTZzg+pTobTfkJPU4GdimyRchcq+K3Jg==", "path": "volo.abp.features/8.1.1", "hashPath": "volo.abp.features.8.1.1.nupkg.sha512"}, "Volo.Abp.Guids/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-3nnNnffKkIKrzl14xlhaPads4GVzPneFSf59w+wKkzTjnbC6cmsWmC+6W7qcVAYFpwioXUpBQLMZj1BQgREwew==", "path": "volo.abp.guids/8.1.1", "hashPath": "volo.abp.guids.8.1.1.nupkg.sha512"}, "Volo.Abp.Http/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PLhOHstLHOtdr03wXVod+mi7MoxKhOJ1RCjJqk71XlGQWLl4YhdcXfrG+rH3+vkRkAn4XyYa/n50InmqemTdZg==", "path": "volo.abp.http/8.1.1", "hashPath": "volo.abp.http.8.1.1.nupkg.sha512"}, "Volo.Abp.Http.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-b2cxbkFPm3tqb7g7xXfpedRnjuuOHytlpLsdWlQ5rS5sWig/3f6uxFSRDoh9DJU34YGa5Gctd6I7IaSYvdMWog==", "path": "volo.abp.http.abstractions/8.1.1", "hashPath": "volo.abp.http.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Http.Client/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-3oM7eGHDXu9TKuAv5hpw8QqhGV5VDPrcCLkB31Apl9xQzBY4hawhaVAoidkLk/dfMQ8i69yyD+9yGTco7TavBg==", "path": "volo.abp.http.client/8.1.1", "hashPath": "volo.abp.http.client.8.1.1.nupkg.sha512"}, "Volo.Abp.Identity.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-BBhSossoDSC2Iq24l4wghHT0Mu+PHByXXKR7j/HczSMD0Y8oV9XQXNscUOavNznp3VXFi9avuq5xqNGqcpIuuQ==", "path": "volo.abp.identity.application.contracts/8.1.1", "hashPath": "volo.abp.identity.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+lvLGuzrwCGQBsx6od6gK/2x5nL6szg6AOS63MtlOn3ZFSDZbkreUSoi2H/AKNLAgN5NPBjwLNEWdAgPu6t2Lw==", "path": "volo.abp.identity.domain.shared/8.1.1", "hashPath": "volo.abp.identity.domain.shared.8.1.1.nupkg.sha512"}, "Volo.Abp.Identity.HttpApi.Client/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Jv1h0CnDUFh8AuV9TS52Ui93kSIbiu6KpJu9IeCka+fw1LgrMqpNL6l3l2gZqCNCrUmOE4g0+4eosL2vf/B/ug==", "path": "volo.abp.identity.httpapi.client/8.1.1", "hashPath": "volo.abp.identity.httpapi.client.8.1.1.nupkg.sha512"}, "Volo.Abp.Json/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PIEnmql9AbeGTph1kOhCnUqB+f00iFaujFwxp68Cx/5d3D7PO5zpzIgLVlASV29++BTmbMZaSzNctEozj0pdrg==", "path": "volo.abp.json/8.1.1", "hashPath": "volo.abp.json.8.1.1.nupkg.sha512"}, "Volo.Abp.Json.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-vn4bXF9KWkcKOz3MjOrn3blA1GAxd0ePCBW29LQHg6GKTUuzvhcV75vNb5NdspErDBbXSL8tY0ujr9/kvCmD+A==", "path": "volo.abp.json.abstractions/8.1.1", "hashPath": "volo.abp.json.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Json.SystemTextJson/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-xtnaPqGReRFFTTiGy5QaMB3kNqd2Ydz9VTqrGouLvc1AaqUF9+KhOOCh9kVA18HEqESrm5VxKqpS2ZvRnEEHvw==", "path": "volo.abp.json.systemtextjson/8.1.1", "hashPath": "volo.abp.json.systemtextjson.8.1.1.nupkg.sha512"}, "Volo.Abp.Localization/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-QECnrtfqsPIlVYHVsa4nilgVTnQbEqmOEjd2gD0oKOEdXQd7iIAhYMHeTbvSO0nwXkq9FztAf2qmCRMDtrbUjw==", "path": "volo.abp.localization/8.1.1", "hashPath": "volo.abp.localization.8.1.1.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Or7NaSIj0wizPtguGUeWW7stoaXUpGr61Y3MRtYjmjIGFxAcrIuX3drlwGK9aB5m6Xt5xQNVkqh4Yy8kEkmNVQ==", "path": "volo.abp.localization.abstractions/8.1.1", "hashPath": "volo.abp.localization.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Minify/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jigvjlYMCjMM1nb6BfB+/j7fh0YUwIriV6hZfX5/7prIwObt5DoxqLwMtzhjaE+GVbAVT3LzUWPd/JZMLHntHA==", "path": "volo.abp.minify/8.1.1", "hashPath": "volo.abp.minify.8.1.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-KRh2YuKoFVwUhe0bX6If+ukXGLnez0IudjocwwUTLJiBZ1IWjMb70OBbEy03LunIuvft/3i5m7uwIqdDBYKeVA==", "path": "volo.abp.multitenancy/8.1.1", "hashPath": "volo.abp.multitenancy.8.1.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vvm4oQsq+uRe29VgUEDiCe8eb2ML2dppcc7YNM1tiiNrDAUBG4lYSiBBvUUABXzS+jS/P1zmkEieU07CFaQSqw==", "path": "volo.abp.multitenancy.abstractions/8.1.1", "hashPath": "volo.abp.multitenancy.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.ObjectExtending/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0K8/fu1xpXcWl2CBCHvf8JP5t9wMt9YMlCjGzaGKP2Y/WQMWtN41yyEeKxYdKCnfcLQ8ZNffZQRPAX6hQKCkQ==", "path": "volo.abp.objectextending/8.1.1", "hashPath": "volo.abp.objectextending.8.1.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-YUerxRU7UQptEGqaBbQ+Kx2Vxdm/bYUn/K07vNuNq9xSwLXzDocMPO6taGKZ1oEs8o0Nnmq/QgKo7f5u7bB4qQ==", "path": "volo.abp.permissionmanagement.application.contracts/8.1.1", "hashPath": "volo.abp.permissionmanagement.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Shared/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-/dXxzRCbr+WMAuuoTc0KrJh4b/JIXtGpkI8nnsHOg5LdzmMF84AnNEM7ZwssQm0slsEJzhfjDMuUrEK2aWRhVw==", "path": "volo.abp.permissionmanagement.domain.shared/8.1.1", "hashPath": "volo.abp.permissionmanagement.domain.shared.8.1.1.nupkg.sha512"}, "Volo.Abp.RemoteServices/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-k4uHXPbFF2lDN4IhSOMHTGcsrgLW6XkaUPKSWTXJE//8ELDg6tu9vRLiTZhuUGIXvrSM35v488ap9pyhyL283Q==", "path": "volo.abp.remoteservices/8.1.1", "hashPath": "volo.abp.remoteservices.8.1.1.nupkg.sha512"}, "Volo.Abp.Security/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UfC7k+F5U7mj1uBwBEfbGAbjbyeuJXxPLbYtlEPqBztXBGM9VnaQ2/gLN3vdgfJiU0TDx6fmbZJm5lCzVclGyQ==", "path": "volo.abp.security/8.1.1", "hashPath": "volo.abp.security.8.1.1.nupkg.sha512"}, "Volo.Abp.Settings/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UT+BsKnvB2y35QCi5+TtFekw66/j8o8GelLLWOwf99uqfi3Xk9ntSRtmvXP/vDij9Kyv/ReGWg/E/kwwqQIblg==", "path": "volo.abp.settings/8.1.1", "hashPath": "volo.abp.settings.8.1.1.nupkg.sha512"}, "Volo.Abp.Threading/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-mMUfVsOoHE5c7CgYQKacZZoEV7+i0HepXUxiBjUfS+mwAu6KS/SDUN9KMFWtRtIbqYycRObTF+LdAQFr8EDq0Q==", "path": "volo.abp.threading/8.1.1", "hashPath": "volo.abp.threading.8.1.1.nupkg.sha512"}, "Volo.Abp.Timing/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+qmruseshbQ2utykpTu/czIAs9kWWS6FMWkV2/nw98/LYMppveX3Wz7TO9t6QsDCWPXOgvtAOU2xd0IB/aiOlw==", "path": "volo.abp.timing/8.1.1", "hashPath": "volo.abp.timing.8.1.1.nupkg.sha512"}, "Volo.Abp.Uow/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Jjg0UFya+vJ5omveIuSbXivFqbNuklsUgydpySgRntGMQt4wBxV04ZlqjxzjDi7uCQ8CkRwaxVZNtbIXgdoTbg==", "path": "volo.abp.uow/8.1.1", "hashPath": "volo.abp.uow.8.1.1.nupkg.sha512"}, "Volo.Abp.Users.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-FIRoheeC7EnfDYi6LQfo4Ud50I5T3zJ+UTZqG+7J1akOBaN1FOenAoRwS54SghdRQ3Ti3sIESe2cW79ywc25fg==", "path": "volo.abp.users.abstractions/8.1.1", "hashPath": "volo.abp.users.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6Kcg36g0li48oRfMw/InqimhFCxydSokZJ0r07fwQgUtSWJ0VsHLUpS0ZlyU0e/X9gK3PMLhlEZMGX6AQs36g==", "path": "volo.abp.users.domain.shared/8.1.1", "hashPath": "volo.abp.users.domain.shared.8.1.1.nupkg.sha512"}, "Volo.Abp.Validation/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ecqXxmkppJxkZ+NkSAqSgDxCoQ5UeQg3AlFJDwb4W13WRHqsS5R4uvvxMvsYdAz87uxOjAieZm+KE3dFtBX6hQ==", "path": "volo.abp.validation/8.1.1", "hashPath": "volo.abp.validation.8.1.1.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lyYP+IY6MGvrCOXM5DPZ3GI4iH3Xc+oXYPzi6kmRrFov8NSYUxCLYfnE2eNC3MqFC2Dw2JUmtumaVJ0g+t/OBQ==", "path": "volo.abp.validation.abstractions/8.1.1", "hashPath": "volo.abp.validation.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-K0kwo4iaxVGro7Av3+wot+oIMqzwcC+xRjCODjznTGMH5d2Mao9WMSonQK3sphSkH7q5kukyKwS1VwS+V0Pg1w==", "path": "volo.abp.virtualfilesystem/8.1.1", "hashPath": "volo.abp.virtualfilesystem.8.1.1.nupkg.sha512"}, "AbpTools.AbpHelper.Gui.Application.Contracts/2.16.0": {"type": "project", "serviceable": false, "sha512": ""}}}