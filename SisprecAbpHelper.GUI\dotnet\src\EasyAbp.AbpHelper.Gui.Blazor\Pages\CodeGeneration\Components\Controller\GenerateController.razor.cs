﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.CodeGeneration.Controller;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.CodeGeneration.Components.Controller
{
    public partial class GenerateController
    {
        [Inject]
        private ICodeGenerationControllerAppService Service { get; set; }

        protected override async Task InternalExecuteAsync()
        {
            await Service.GenerateAsync(Input);
        }
    }
}
