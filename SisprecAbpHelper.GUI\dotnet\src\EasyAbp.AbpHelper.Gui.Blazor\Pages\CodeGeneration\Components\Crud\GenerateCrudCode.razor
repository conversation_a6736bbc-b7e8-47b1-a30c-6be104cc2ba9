﻿@using Volo.Abp.AspNetCore.Components.Web
@using AbpTools.AbpHelper.Gui.Localization
@using AbpTools.AbpHelper.Gui.CodeGeneration.Crud.Dtos;
@using AbpTools.AbpHelper.Gui.Blazor.Pages.Shared;
@inherits ExecutableComponentBaseWithDirectory<AbpHelperGenerateCrudInput>
@inject AbpBlazorMessageLocalizerHelper<GuiResource> Lh

<Card Class="mb-4">
    <CardBody>
        <Alert Color="Color.Info"  Visible>
            <AlertDescription>
                <Icon Name="IconName.QuestionCircle" /> @L["Info:FunctionIsBasedOnAbpHelperPart1"]
            </AlertDescription>
            <AlertMessage>
                <Icon Name="IconName.ExternalLinkSquareAlt"/>
                <Link To="https://github.com/EasyAbp/AbpHelper.CLI/blob/develop/src/AbpHelper.Core/Commands/Generate/Crud/CrudCommandOption.cs" Target="Target.Blank" Style="text-decoration: none">
                    @L["Info:FunctionIsBasedOnAbpHelperPart2_SourceCode"]
                </Link>
            </AlertMessage>
            <AlertDescription>
                @L["Info:FunctionIsBasedOnAbpHelperPart3"]
            </AlertDescription>
        </Alert>
        <Form>
            <Validations @ref="ValidationsRef" Model="Input" ValidateOnLoad="false">
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["Directory"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Directory" Autofocus="true">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["CodeGeneration_Crud:Entity"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Entity">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipGetListInputDto">@L["CodeGeneration_Crud:SkipGetListInputDto"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SeparateDto">@L["CodeGeneration_Crud:SeparateDto"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.EntityPrefixDto">@L["CodeGeneration_Crud:EntityPrefixDto"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipPermissions">@L["CodeGeneration_Crud:SkipPermissions"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipCustomRepository">@L["CodeGeneration_Crud:SkipCustomRepository"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipDbMigrations">@L["CodeGeneration_Crud:SkipDbMigrations"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipUi">@L["CodeGeneration_Crud:SkipUi"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipViewModel">@L["CodeGeneration_Crud:SkipViewModel"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipLocalization">@L["CodeGeneration_Crud:SkipLocalization"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipTest">@L["CodeGeneration_Crud:SkipTest"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipEntityConstructors">@L["CodeGeneration_Crud:SkipEntityConstructors"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.NoOverwrite">@L["NoOverwrite"]</Check>
                </Field>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["CodeGeneration_Crud:DtoSuffix"]</FieldLabel>
                        <TextEdit @bind-Text="Input.DtoSuffix">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["CodeGeneration_Crud:MigrationProjectName"]</FieldLabel>
                        <TextEdit @bind-Text="Input.MigrationProjectName">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["ProjectName"]</FieldLabel>
                        <TextEdit @bind-Text="Input.ProjectName">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["Exclude"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Exclude">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>

                <SubmitButton Block="true" Clicked="@ExecuteAsync" SaveResourceKey="Button:Execute" />
            </Validations>
        </Form>
    </CardBody>
</Card>