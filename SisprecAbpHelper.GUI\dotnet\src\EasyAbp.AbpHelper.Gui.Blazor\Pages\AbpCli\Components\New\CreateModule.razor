﻿@using Volo.Abp.AspNetCore.Components.Web
@using AbpTools.AbpHelper.Gui.Localization
@using AbpTools.AbpHelper.Gui.Blazor.Pages.Shared;
@using AbpTools.AbpHelper.Gui.AbpCli.New.Dtos
@inherits ExecutableComponentBase
@inject AbpBlazorMessageLocalizerHelper<GuiResource> Lh

<Card Class="mb-4">
    <CardBody>
        <Alert Color="Color.Info"  Visible>
            <AlertDescription>
                <Icon Name="IconName.QuestionCircle" /> @L["Info:FunctionIsBasedOnAbpCliPart1"]
            </AlertDescription>
            <AlertMessage>
                <Icon Name="IconName.ExternalLinkSquareAlt"/>
                <Link To="https://docs.abp.io/en/abp/latest/CLI#new" Target="Target.Blank" Style="text-decoration: none">
                    @L["Info:FunctionIsBasedOnAbpCliPart2_Document"]
                </Link>
            </AlertMessage>
            <AlertDescription>
                @L["Info:FunctionIsBasedOnAbpCliPart3"]
            </AlertDescription>
        </Alert>
        <Form>
            <Validations @ref="ValidationsRef" Model="Input" ValidateOnLoad="false">
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_New:SolutionName"]</FieldLabel>
                        <TextEdit @bind-Text="Input.SolutionName" Autofocus="true">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_New:OutputFolder"]</FieldLabel>
                        <TextEdit @bind-Text="Input.OutputFolder">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_New:Version"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Version">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_New:TemplateSource"]</FieldLabel>
                        <TextEdit @bind-Text="Input.TemplateSource">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_New:ConnectionString"]</FieldLabel>
                        <TextEdit @bind-Text="Input.ConnectionString">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Field>
                    <FieldLabel>@L["AbpCli_New:DatabaseManagementSystem"]</FieldLabel>
                    <Select TValue="Database" @bind-SelectedValue="Input.DatabaseManagementSystem">
                        @foreach (var database in Enum.GetValues<Database>())
                        {
                            <SelectItem Value="database">
                                @L[$"AbpCli_New:Database:{Enum.GetName(database)}"]
                            </SelectItem>
                        }
                    </Select>
                </Field>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["AbpCli_New:LocalFrameworkRef"]</FieldLabel>
                        <TextEdit @bind-Text="Input.LocalFrameworkRef">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.NoUi">@L["AbpCli_New:NoUi"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.Preview">@L["AbpCli_New:Preview"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.CreateSolutionFolder">@L["AbpCli_New:CreateSolutionFolder"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.NoRandomPort">@L["AbpCli_New:NoRandomPort"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipInstallingLibs">@L["AbpCli_New:SkipInstallingLibs"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.SkipCache">@L["AbpCli_New:SkipCache"]</Check>
                </Field>
                <Field>
                    <Check TValue="bool" @bind-Checked="@Input.WithPublicWebsite">@L["AbpCli_New:WithPublicWebsite"]</Check>
                </Field>

                <SubmitButton Block="true" Clicked="@ExecuteAsync" SaveResourceKey="Button:Execute" />
            </Validations>
        </Form>
    </CardBody>
</Card>