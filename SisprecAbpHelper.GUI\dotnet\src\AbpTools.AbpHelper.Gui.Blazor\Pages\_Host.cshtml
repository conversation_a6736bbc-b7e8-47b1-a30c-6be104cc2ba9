@page "/"
@namespace AbpTools.AbpHelper.Gui.Blazor.Pages
@using System.Globalization
@using Volo.Abp.AspNetCore.Components.Web.LeptonXLiteTheme.Themes.LeptonXLite
@using Volo.Abp.AspNetCore.Components.Server.LeptonXLiteTheme.Bundling
@using Volo.Abp.Localization

@{
    Layout = null;
    var rtl = CultureHelper.IsRtl ? "rtl" : string.Empty;
}

<script>
    function openErrorLogFile() {
        window.open('/logs/recent-error-log-file', '_blank');
    }
</script>

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentCulture.Name" dir="@rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AbpTools.AbpHelper.Gui.Blazor</title>
    <base href="~/" />
    
    <abp-style-bundle name="@BlazorLeptonXLiteThemeBundles.Styles.Global" />
</head>
<body class="abp-application-layout bg-light @rtl">
    <component type="typeof(App)" render-mode="Server" />

    <div id="blazor-error-ui">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <button onclick="openErrorLogFile()">Show logs</button>
        <a class="dismiss">🗙</a>
    </div>

    <abp-script-bundle name="@BlazorLeptonXLiteThemeBundles.Scripts.Global" />
    <script src="_framework/blazor.server.js"></script>
    <script src="_content/Blazorx-Analytics/blazor-analytics.js"></script>
</body>
</html>
