﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.CodeGeneration.AppService;
using AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;

namespace AbpTools.AbpHelper.Gui.Controllers.CodeGeneration
{
    [RemoteService]
    [Route("/api/abp-helper/code-generation/app-service")]
    public class CodeGenerationAppServiceController : GuiController, ICodeGenerationAppServiceAppService
    {
        private readonly ICodeGenerationAppServiceAppService _service;

        public CodeGenerationAppServiceController(ICodeGenerationAppServiceAppService service)
        {
            _service = service;
        }
        
        [HttpPost]
        [Route("class")]
        public Task<ServiceExecutionResult> GenerateClassAsync(AbpHelperGenerateAppServiceClassInput input)
        {
            return _service.GenerateClassAsync(input);
        }

        [HttpPost]
        [Route("methods")]
        public Task<ServiceExecutionResult> GenerateMethodsAsync(AbpHelperGenerateAppServiceMethodsInput input)
        {
            return _service.GenerateMethodsAsync(input);
        }
    }
}