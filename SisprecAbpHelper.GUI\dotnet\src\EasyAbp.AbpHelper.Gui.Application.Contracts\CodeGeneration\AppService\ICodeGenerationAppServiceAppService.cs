using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.AppService
{
    public interface ICodeGenerationAppServiceAppService : IApplicationService
    {
        Task<ServiceExecutionResult> GenerateClassAsync(AbpHelperGenerateAppServiceClassInput input);
        
        Task<ServiceExecutionResult> GenerateMethodsAsync(AbpHelperGenerateAppServiceMethodsInput input);
    }
}