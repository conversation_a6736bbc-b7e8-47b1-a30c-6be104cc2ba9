2025-06-02 04:28:00.282 -03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Using ExpandEndpoint requires that the replaced endpoint have a unique priority. The following endpoints were found with the same priority:
Fallback {*path:nonfile}
Fallback {*path:nonfile}
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ValidateUniqueScore(Int32 index)
   at Microsoft.AspNetCore.Routing.Matching.CandidateSet.ExpandEndpoint(Int32 index, IReadOnlyList`1 endpoints, IComparer`1 comparer)
   at Microsoft.AspNetCore.Mvc.RazorPages.Infrastructure.DynamicPageEndpointMatcherPolicy.ApplyAsync(HttpContext httpContext, CandidateSet candidates)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.SelectEndpointWithPoliciesAsync(HttpContext httpContext, IEndpointSelectorPolicy[] policies, CandidateSet candidateSet)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.<Invoke>g__AwaitMatch|10_1(EndpointRoutingMiddleware middleware, HttpContext httpContext, Task matchTask)
   at Volo.Abp.AspNetCore.Tracing.AbpCorrelationIdMiddleware.InvokeAsync(HttpContext context, RequestDelegate next)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.InterfaceMiddlewareBinder.<>c__DisplayClass2_0.<<CreateMiddleware>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
