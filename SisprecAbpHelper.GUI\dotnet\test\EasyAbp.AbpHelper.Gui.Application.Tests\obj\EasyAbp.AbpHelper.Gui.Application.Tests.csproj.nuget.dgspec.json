{"format": 1, "restore": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.Application.Tests\\EasyAbp.AbpHelper.Gui.Application.Tests.csproj": {}}, "projects": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj", "projectName": "AbpTools.AbpHelper.Core", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Bogus": {"target": "Package", "version": "[35.5.1, )"}, "ConfigureAwait.Fody": {"suppressParent": "All", "target": "Package", "version": "[3.3.2, )"}, "Elsa": {"target": "Package", "version": "[1.2.2.29, )"}, "Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers", "suppressParent": "All", "target": "Package", "version": "[6.8.0, )"}, "Humanizer.Core": {"target": "Package", "version": "[2.14.1, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[4.5.0, )"}, "Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[8.0.0, )"}, "NuGet.Protocol": {"target": "Package", "version": "[6.2.2, )"}, "Scriban": {"target": "Package", "version": "[5.10.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "SourceLink.Create.CommandLine": {"suppressParent": "All", "target": "Package", "version": "[2.8.3, )"}, "System.CommandLine": {"target": "Package", "version": "[2.0.0-beta1.20371.2, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Core": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Http": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.VirtualFileSystem": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj": {"version": "2.16.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj", "projectName": "EasyAbp.AbpHelper.Gui.Application.Contracts", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": {"target": "Package", "version": "[9.0.0, )"}, "Volo.Abp.Account.Application.Contracts": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Identity.Application.Contracts": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.ObjectExtending": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application\\EasyAbp.AbpHelper.Gui.Application.csproj": {"version": "2.16.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application\\EasyAbp.AbpHelper.Gui.Application.csproj", "projectName": "EasyAbp.AbpHelper.Gui.Application", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application\\EasyAbp.AbpHelper.Gui.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj": {"projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\AbpHelper.CLI\\src\\AbpHelper.Core\\AbpHelper.Core.csproj"}, "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj": {"projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application.Contracts\\EasyAbp.AbpHelper.Gui.Application.Contracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.AutoMapper": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Cli.Core": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Ddd.Application": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.Application.Tests\\EasyAbp.AbpHelper.Gui.Application.Tests.csproj": {"version": "2.16.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.Application.Tests\\EasyAbp.AbpHelper.Gui.Application.Tests.csproj", "projectName": "EasyAbp.AbpHelper.Gui.Application.Tests", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.Application.Tests\\EasyAbp.AbpHelper.Gui.Application.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.Application.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application\\EasyAbp.AbpHelper.Gui.Application.csproj": {"projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\src\\EasyAbp.AbpHelper.Gui.Application\\EasyAbp.AbpHelper.Gui.Application.csproj"}, "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.TestBase\\EasyAbp.AbpHelper.Gui.TestBase.csproj": {"projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.TestBase\\EasyAbp.AbpHelper.Gui.TestBase.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.TestBase\\EasyAbp.AbpHelper.Gui.TestBase.csproj": {"version": "2.16.0", "restore": {"projectUniqueName": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.TestBase\\EasyAbp.AbpHelper.Gui.TestBase.csproj", "projectName": "EasyAbp.AbpHelper.Gui.TestBase", "projectPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.TestBase\\EasyAbp.AbpHelper.Gui.TestBase.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\test\\EasyAbp.AbpHelper.Gui.TestBase\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Sisprec-AbpHelper\\SisprecAbpHelper.GUI\\dotnet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/blazorise/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.11.1, )"}, "NSubstitute": {"target": "Package", "version": "[5.1.0, )"}, "Shouldly": {"target": "Package", "version": "[4.2.1, )"}, "Volo.Abp.Authorization": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.1.1, )"}, "Volo.Abp.TestBase": {"target": "Package", "version": "[8.1.1, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.extensibility.execution": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}