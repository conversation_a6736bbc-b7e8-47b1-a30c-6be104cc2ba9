<Project>
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <Version>1.0.0</Version>
    <PackageVersion>1.0.0</PackageVersion>
    <AssemblyVersion>1.0.0</AssemblyVersion>
    <FileVersion>1.0.0</FileVersion>
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <Authors>AbpTools</Authors>
    <Description>AbpTools is a package tool to help you with developing Abp vNext applications.</Description>
    <Copyright>AbpTools</Copyright>
    <Company>AbpTools</Company>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SourceLink.Create.CommandLine" Version="2.8.3" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ConfigureAwait.Fody" Version="3.3.2" PrivateAssets="All" />
    <PackageReference Include="Fody" Version="6.8.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
