﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Bundle.Dtos;
using AbpTools.AbpHelper.Gui.Common;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Cli.Commands;

namespace AbpTools.AbpHelper.Gui.AbpCli.Bundle
{
    public class AbpCliBundleAppService : AbpCliAppService, IAbpCliBundleAppService
    {
        private readonly BundleCommand _bundleCommand;
        private readonly ICurrentDirectoryHelper _currentDirectoryHelper;

        public AbpCliBundleAppService(
            BundleCommand bundleCommand,
            ICurrentDirectoryHelper currentDirectoryHelper)
        {
            _bundleCommand = bundleCommand;
            _currentDirectoryHelper = currentDirectoryHelper;
        }

        public virtual async Task<ServiceExecutionResult> RunAsync(AbpBundleInput input)
        {
            var args = CreateCommandLineArgs(input, "abp bundle");

            using (_currentDirectoryHelper.Change(input.Directory))
            {
                await _bundleCommand.ExecuteAsync(args);
            }

            return new ServiceExecutionResult(true);
        }
    }
}