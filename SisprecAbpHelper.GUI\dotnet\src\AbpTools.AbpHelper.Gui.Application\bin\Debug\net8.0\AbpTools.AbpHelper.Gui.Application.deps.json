{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AbpTools.AbpHelper.Gui.Application/2.16.0": {"dependencies": {"AbpTools.AbpHelper.Core": "1.0.0", "AbpTools.AbpHelper.Gui.Application.Contracts": "2.16.0", "Volo.Abp.AutoMapper": "8.1.1", "Volo.Abp.Cli.Core": "8.1.1", "Volo.Abp.Ddd.Application": "8.1.1", "AbpTools.AbpHelper.Core.Reference": "*******"}, "runtime": {"AbpTools.AbpHelper.Gui.Application.dll": {}}}, "AsyncKeyedLock/6.3.4": {"runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"assemblyVersion": "6.3.4.0", "fileVersion": "6.3.4.0"}}}, "Autofac/8.0.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Autofac.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autofac.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Autofac": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "Autofac.Extras.DynamicProxy/7.1.0": {"dependencies": {"Autofac": "8.0.0", "Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "Bogus/35.5.1": {"runtime": {"lib/net6.0/Bogus.dll": {"assemblyVersion": "35.5.1.0", "fileVersion": "35.5.1.0"}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "DeepL.net/1.8.0": {"dependencies": {"Microsoft.Extensions.Http.Polly": "5.0.1", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net5.0/DeepL.net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa/********": {"dependencies": {"Elsa.Activities.Console": "********", "Elsa.Activities.ControlFlow": "********", "Elsa.Activities.Email": "********", "Elsa.Activities.Http": "********", "Elsa.Activities.Timers": "********", "Elsa.Activities.UserTask": "********", "Elsa.Activities.Workflows": "********", "Elsa.Core": "********", "Elsa.Scripting.Liquid": "********"}, "runtime": {"lib/netstandard2.0/Elsa.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Abstractions/********": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0", "Newtonsoft.Json": "13.0.3", "NodaTime": "3.0.0-beta01", "NodaTime.Serialization.JsonNet": "2.2.0", "System.ComponentModel.Annotations": "4.7.0"}, "runtime": {"lib/netstandard2.0/Elsa.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Activities.Console/********": {"dependencies": {"Elsa.Core": "********"}, "runtime": {"lib/netstandard2.0/Elsa.Activities.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Activities.ControlFlow/********": {"dependencies": {"Elsa.Core": "********", "Elsa.Scripting.JavaScript": "********"}, "runtime": {"lib/netstandard2.0/Elsa.Activities.ControlFlow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Activities.Email/********": {"dependencies": {"Elsa.Core": "********", "MailKit": "2.4.1"}, "runtime": {"lib/netstandard2.0/Elsa.Activities.Email.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Activities.Http/********": {"dependencies": {"Elsa.Core": "********", "Elsa.Scripting.JavaScript": "********", "Elsa.Scripting.Liquid": "********", "Microsoft.AspNetCore.DataProtection": "3.1.1", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.Extensions.Http": "8.0.0"}, "runtime": {"lib/netstandard2.0/Elsa.Activities.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Activities.Timers/********": {"dependencies": {"Elsa.Core": "********", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "NCrontab": "3.3.1"}, "runtime": {"lib/netstandard2.0/Elsa.Activities.Timers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Activities.UserTask/********": {"dependencies": {"Elsa.Core": "********"}, "runtime": {"lib/netstandard2.0/Elsa.Activities.UserTask.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Activities.Workflows/********": {"dependencies": {"Elsa.Core": "********", "Elsa.Scripting.JavaScript": "********"}, "runtime": {"lib/netstandard2.0/Elsa.Activities.Workflows.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.AutoMapper.Extensions/********": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "NodaTime": "3.0.0-beta01"}, "runtime": {"lib/netstandard2.0/Elsa.AutoMapper.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Core/********": {"dependencies": {"AutoMapper": "12.0.1", "Elsa.Abstractions": "********", "Elsa.AutoMapper.Extensions": "********", "Elsa.Scrutor": "********", "Humanizer.Core": "2.14.1", "MediatR": "8.0.0", "MediatR.Extensions.Microsoft.DependencyInjection": "8.0.0", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Console": "3.1.1", "Microsoft.Extensions.Logging.Debug": "3.1.1", "NodaTime.Serialization.JsonNet": "2.2.0", "YamlDotNet": "8.0.0"}, "runtime": {"lib/netstandard2.0/Elsa.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Scripting.JavaScript/********": {"dependencies": {"Elsa.Core": "********", "Jint": "3.0.0-beta-1629"}, "runtime": {"lib/netstandard2.0/Elsa.Scripting.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Scripting.Liquid/********": {"dependencies": {"Elsa.Core": "********", "Fluid.Core": "1.0.0-beta-9605"}, "runtime": {"lib/netstandard2.0/Elsa.Scripting.Liquid.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elsa.Scrutor/********": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyModel": "3.1.1"}, "runtime": {"lib/netstandard2.0/Elsa.Scrutor.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Esprima/1.0.1246": {"runtime": {"lib/netstandard2.0/Esprima.dll": {"assemblyVersion": "1.0.1246.0", "fileVersion": "1.0.1246.0"}}}, "Fluid.Core/1.0.0-beta-9605": {"dependencies": {"Irony.Core": "1.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netstandard2.1/Fluid.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "********", "fileVersion": "2.14.1.48190"}}}, "IdentityModel/6.2.0": {"runtime": {"lib/net6.0/IdentityModel.dll": {"assemblyVersion": "6.2.0.0", "fileVersion": "6.2.0.0"}}}, "Irony.Core/1.0.7": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Reflection": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Numerics": "4.3.0"}, "runtime": {"lib/netstandard1.6/Irony.dll": {"assemblyVersion": "1.0.7.0", "fileVersion": "1.0.7.0"}}}, "JetBrains.Annotations/2023.3.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2023.3.0.0"}}}, "Jint/3.0.0-beta-1629": {"dependencies": {"Esprima": "1.0.1246"}, "runtime": {"lib/netstandard2.0/Jint.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "*******"}}}, "LibGit2Sharp/0.28.0": {"dependencies": {"LibGit2Sharp.NativeBinaries": "2.0.320"}, "runtime": {"lib/net6.0/LibGit2Sharp.dll": {"assemblyVersion": "0.28.0.0", "fileVersion": "0.28.0.0"}}}, "LibGit2Sharp.NativeBinaries/2.0.320": {"runtimeTargets": {"runtimes/linux-arm/native/libgit2-e632535.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libgit2-e632535.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libgit2-e632535.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libgit2-e632535.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libgit2-e632535.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libgit2-e632535.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libgit2-e632535.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgit2-e632535.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/git2-e632535.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.6.4.0"}, "runtimes/win-x64/native/git2-e632535.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.6.4.0"}, "runtimes/win-x86/native/git2-e632535.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.6.4.0"}}}, "MailKit/2.4.1": {"dependencies": {"MimeKit": "2.4.1", "System.Net.NameResolution": "4.3.0", "System.Net.Security": "4.3.2", "System.Runtime.Serialization.Primitives": "4.3.0"}, "runtime": {"lib/netstandard2.0/MailKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MediatR/8.0.0": {"runtime": {"lib/netstandard2.0/MediatR.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MediatR.Extensions.Microsoft.DependencyInjection/8.0.0": {"dependencies": {"MediatR": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/MediatR.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authorization/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Cryptography.Internal/3.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.119.61510"}}}, "Microsoft.AspNetCore.DataProtection/3.1.1": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "3.1.1", "Microsoft.AspNetCore.DataProtection.Abstractions": "3.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Win32.Registry": "4.7.0", "System.Security.Cryptography.Xml": "4.7.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.119.61510"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/3.1.1": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.119.61510"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.19024"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Metadata/8.0.0": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyModel": "3.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.19109"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyModel/3.1.1": {"dependencies": {"System.Text.Json": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.119.60804"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Http.Polly/5.0.1": {"dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Polly": "8.2.0", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.120.58002"}}}, "Microsoft.Extensions.Localization/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Configuration/3.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.119.61404"}}}, "Microsoft.Extensions.Logging.Console/3.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "3.1.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.119.61404"}}}, "Microsoft.Extensions.Logging.Debug/3.1.1": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.119.61404"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "MimeKit/2.4.1": {"dependencies": {"Portable.BouncyCastle": "1.8.5", "System.Data.Common": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netstandard2.0/MimeKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NCrontab/3.3.1": {"runtime": {"lib/netstandard2.0/NCrontab.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "NodaTime/3.0.0-beta01": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/NodaTime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NodaTime.Serialization.JsonNet/2.2.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "NodaTime": "3.0.0-beta01"}, "runtime": {"lib/netstandard2.0/NodaTime.Serialization.JsonNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Common/6.2.2": {"dependencies": {"NuGet.Frameworks": "6.2.2"}, "runtime": {"lib/netstandard2.0/NuGet.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Configuration/6.2.2": {"dependencies": {"NuGet.Common": "6.2.2", "System.Security.Cryptography.ProtectedData": "4.4.0"}, "runtime": {"lib/netstandard2.0/NuGet.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Frameworks/6.2.2": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Packaging/6.2.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "NuGet.Configuration": "6.2.2", "NuGet.Versioning": "6.7.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Pkcs": "5.0.0"}, "runtime": {"lib/net5.0/NuGet.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Protocol/6.2.2": {"dependencies": {"NuGet.Packaging": "6.2.2"}, "runtime": {"lib/net5.0/NuGet.Protocol.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Versioning/6.7.0": {"runtime": {"lib/netstandard2.0/NuGet.Versioning.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "NUglify/1.21.0": {"runtime": {"lib/net5.0/NUglify.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Polly/8.2.0": {"dependencies": {"Polly.Core": "8.2.0"}, "runtime": {"lib/net6.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.0.2702"}}}, "Polly.Core/8.2.0": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.2.0.2702"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "8.2.0"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Portable.BouncyCastle/1.8.5": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Security/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "Scriban/5.10.0": {"runtime": {"lib/net7.0/Scriban.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.10.0.0"}}}, "Serilog/4.0.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StackExchange.Redis/2.7.4": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.7.4.20928"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.CommandLine/2.0.0-beta1.20371.2": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.20.37102"}}}, "System.ComponentModel.Annotations/4.7.0": {}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Diagnostics.EventLog/6.0.0": {"runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Formats.Asn1/5.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/5.0.1": {"runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.120.57516"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.3.5": {"runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.7.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Security/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Security": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/6.0.1": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "5.0.0.0", "fileVersion": "5.0.20.51904"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.4.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/4.7.0": {"dependencies": {"System.Security.Cryptography.Pkcs": "5.0.0", "System.Security.Permissions": "8.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Permissions/8.0.0": {"dependencies": {"System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.2": {}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/8.0.0": {"runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.2"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "TimeZoneConverter/6.1.0": {"runtime": {"lib/net6.0/TimeZoneConverter.dll": {"assemblyVersion": "6.1.0.0", "fileVersion": "6.1.0.0"}}}, "Volo.Abp.Account.Application.Contracts/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Identity.Application.Contracts": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Account.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Auditing/8.1.1": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.1", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Security": "8.1.1", "Volo.Abp.Threading": "8.1.1", "Volo.Abp.Timing": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Auditing.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization/8.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Security": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization.Abstractions/8.1.1": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.0", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Autofac/8.1.1": {"dependencies": {"Autofac": "8.0.0", "Autofac.Extensions.DependencyInjection": "9.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Volo.Abp.Castle.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Autofac.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.AutoMapper/8.1.1": {"dependencies": {"AutoMapper": "12.0.1", "Volo.Abp.Auditing": "8.1.1", "Volo.Abp.ObjectExtending": "8.1.1", "Volo.Abp.ObjectMapping": "8.1.1"}, "runtime": {"lib/netstandard2.1/Volo.Abp.AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BackgroundWorkers/8.1.1": {"dependencies": {"Volo.Abp.Threading": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Caching/8.1.1": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "Volo.Abp.Json": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Serialization": "8.1.1", "Volo.Abp.Threading": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Castle.Core/8.1.1": {"dependencies": {"Castle.Core": "5.1.1", "Castle.Core.AsyncInterceptor": "2.1.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Cli.Core/8.1.1": {"dependencies": {"DeepL.net": "1.8.0", "LibGit2Sharp": "0.28.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Versioning": "6.7.0", "Polly": "8.2.0", "Polly.Extensions.Http": "3.0.0", "SharpZipLib": "1.4.2", "StackExchange.Redis": "2.7.4", "System.Security.Permissions": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0", "Volo.Abp.Ddd.Domain": "8.1.1", "Volo.Abp.Http": "8.1.1", "Volo.Abp.IdentityModel": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.Localization": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Cli.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/8.1.1": {"dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Data/8.1.1": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.ObjectExtending": "8.1.1", "Volo.Abp.Uow": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application/8.1.1": {"dependencies": {"Volo.Abp.Authorization": "8.1.1", "Volo.Abp.Ddd.Application.Contracts": "8.1.1", "Volo.Abp.Ddd.Domain": "8.1.1", "Volo.Abp.Features": "8.1.1", "Volo.Abp.GlobalFeatures": "8.1.1", "Volo.Abp.Http.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.ObjectMapping": "8.1.1", "Volo.Abp.Security": "8.1.1", "Volo.Abp.Settings": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.1.1", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Domain/8.1.1": {"dependencies": {"Volo.Abp.Auditing": "8.1.1", "Volo.Abp.Caching": "8.1.1", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Ddd.Domain.Shared": "8.1.1", "Volo.Abp.EventBus": "8.1.1", "Volo.Abp.ExceptionHandling": "8.1.1", "Volo.Abp.Guids": "8.1.1", "Volo.Abp.ObjectMapping": "8.1.1", "Volo.Abp.Specifications": "8.1.1", "Volo.Abp.Timing": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Domain.Shared/8.1.1": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.DistributedLocking.Abstractions/8.1.1": {"dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus/8.1.1": {"dependencies": {"Volo.Abp.BackgroundWorkers": "8.1.1", "Volo.Abp.DistributedLocking.Abstractions": "8.1.1", "Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.Guids": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.ObjectExtending": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ExceptionHandling/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Features/8.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.GlobalFeatures/8.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Guids/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Http/8.1.1": {"dependencies": {"Volo.Abp.Http.Abstractions": "8.1.1", "Volo.Abp.Json": "8.1.1", "Volo.Abp.Minify": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Http.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Application.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Authorization": "8.1.1", "Volo.Abp.Identity.Domain.Shared": "8.1.1", "Volo.Abp.PermissionManagement.Application.Contracts": "8.1.1", "Volo.Abp.Users.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Identity.Domain.Shared/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Auditing.Contracts": "8.1.1", "Volo.Abp.Features": "8.1.1", "Volo.Abp.Users.Domain.Shared": "8.1.1", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.IdentityModel/8.1.1": {"dependencies": {"IdentityModel": "6.2.0", "Microsoft.Extensions.Http": "8.0.0", "Volo.Abp.Caching": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1", "Volo.Abp.Threading": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.IdentityModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json/8.1.1": {"dependencies": {"Volo.Abp.Json.SystemTextJson": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.SystemTextJson/8.1.1": {"dependencies": {"System.Text.Json": "8.0.0", "Volo.Abp.Data": "8.1.1", "Volo.Abp.Json.Abstractions": "8.1.1", "Volo.Abp.Timing": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization/8.1.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Settings": "8.1.1", "Volo.Abp.Threading": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Minify/8.1.1": {"dependencies": {"NUglify": "1.21.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Minify.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy/8.1.1": {"dependencies": {"Volo.Abp.Data": "8.1.1", "Volo.Abp.EventBus.Abstractions": "8.1.1", "Volo.Abp.MultiTenancy.Abstractions": "8.1.1", "Volo.Abp.Security": "8.1.1", "Volo.Abp.Settings": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Localization": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectExtending/8.1.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Validation.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectMapping/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Application.Contracts/8.1.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.1.1", "Volo.Abp.Ddd.Application.Contracts": "8.1.1", "Volo.Abp.PermissionManagement.Domain.Shared": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.PermissionManagement.Domain.Shared/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Validation": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Security/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Serialization/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Settings/8.1.1": {"dependencies": {"Volo.Abp.Data": "8.1.1", "Volo.Abp.Localization.Abstractions": "8.1.1", "Volo.Abp.Security": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Specifications/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Specifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Threading/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Timing/8.1.1": {"dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.1.1", "Volo.Abp.Settings": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Uow/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.EventBus": "8.1.1", "Volo.Abp.MultiTenancy": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Users.Domain.Shared/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation/8.1.1": {"dependencies": {"Volo.Abp.Localization": "8.1.1", "Volo.Abp.Validation.Abstractions": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation.Abstractions/8.1.1": {"dependencies": {"Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.VirtualFileSystem/8.1.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.1.1"}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "YamlDotNet/8.0.0": {"runtime": {"lib/netstandard2.1/YamlDotNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AbpTools.AbpHelper.Core/1.0.0": {"dependencies": {"Bogus": "35.5.1", "Elsa": "********", "Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "NuGet.Protocol": "6.2.2", "Scriban": "5.10.0", "Serilog.Extensions.Logging": "8.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "System.CommandLine": "2.0.0-beta1.20371.2", "Volo.Abp.Autofac": "8.1.1", "Volo.Abp.Core": "8.1.1", "Volo.Abp.Http": "8.1.1", "Volo.Abp.VirtualFileSystem": "8.1.1"}, "runtime": {"AbpTools.AbpHelper.Core.dll": {"assemblyVersion": "1.0.0", "fileVersion": ""}}}, "AbpTools.AbpHelper.Gui.Application.Contracts/2.16.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Account.Application.Contracts": "8.1.1", "Volo.Abp.Identity.Application.Contracts": "8.1.1", "Volo.Abp.ObjectExtending": "8.1.1"}, "runtime": {"AbpTools.AbpHelper.Gui.Application.Contracts.dll": {"assemblyVersion": "2.16.0", "fileVersion": "2.16.0.0"}}}, "AbpTools.AbpHelper.Core.Reference/*******": {"runtime": {"AbpTools.AbpHelper.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AbpTools.AbpHelper.Gui.Application/2.16.0": {"type": "project", "serviceable": false, "sha512": ""}, "AsyncKeyedLock/6.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "path": "asynckeyedlock/6.3.4", "hashPath": "asynckeyedlock.6.3.4.nupkg.sha512"}, "Autofac/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qxVqJcl3fixxa5aZc9TmIuYTwooI9GCL5RzfUiTZtTlbAF3NcWz7bPeEyJEAyS/0qGhSyGnXeku2eiu/7L+3qw==", "path": "autofac/8.0.0", "hashPath": "autofac.8.0.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tf+//4MBola256qaaVQqQ6tx2R57S8A8BFekRWNpHkpSFzRBPkU+/fEDUSrCjqldK/B2zRoDbsMcQmYy3PYGWg==", "path": "autofac.extensions.dependencyinjection/9.0.0", "hashPath": "autofac.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6Szv7A1LK/cTdeoyqS45zb/BC5vep8+86f6C1oh2UhZaYtiijlNfLWamp3lxe0uUQ33kFe1dDCjsvfwJWzLg==", "path": "autofac.extras.dynamicproxy/7.1.0", "hashPath": "autofac.extras.dynamicproxy.7.1.0.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "Bogus/35.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-GBp2peww04szEJ0GlBlzCYilFDaSkL7Ja4m37YeTRjC/mgoiZnUuifmgUxVD/oLU2MC1uoXfQYK91doGp7Zkpg==", "path": "bogus/35.5.1", "hashPath": "bogus.35.5.1.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "DeepL.net/1.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Fo7GAm5D576EGbMwAdZr80t8mecsqxGw4AiWyJVinWGjKfdd4eq5GYQhw0Tz4fT4sUnoYric9uJjlUVUoSH6Sw==", "path": "deepl.net/1.8.0", "hashPath": "deepl.net.1.8.0.nupkg.sha512"}, "Elsa/********": {"type": "package", "serviceable": true, "sha512": "sha512-8PJmBjreOKyAOpEa4QURJKYEVuQlMqrUZHnhwX+XvoA6azyL8eZ5dSJxvgHXknTBu/EeE+7LXNrAZUoJ9BEhQQ==", "path": "elsa/********", "hashPath": "elsa.********.nupkg.sha512"}, "Elsa.Abstractions/********": {"type": "package", "serviceable": true, "sha512": "sha512-pn07K6oEvJp22Z/K4AsCpNUl3/fF63//57H1XPsnSa/FKchmWhoQKw8m+q473OLDyhIPk7/h5FhNJq8CP8lI9A==", "path": "elsa.abstractions/********", "hashPath": "elsa.abstractions.********.nupkg.sha512"}, "Elsa.Activities.Console/********": {"type": "package", "serviceable": true, "sha512": "sha512-h7b69GE8giyo8jLM8v13bbBaOtFpNPygfBjlgBOPSQtLaoOPTs/5QZ00aXqHJZH5AwR01NXd+zWVw7OcHoPLTg==", "path": "elsa.activities.console/********", "hashPath": "elsa.activities.console.********.nupkg.sha512"}, "Elsa.Activities.ControlFlow/********": {"type": "package", "serviceable": true, "sha512": "sha512-ozyAri8xDy2GpO1+Zx0vQ6qKJBtXQgYuUCZh0SEZqeQjikW0mBMIQJKkL31G2aJiDn6ZT0d7vOdjN+O5h22pVw==", "path": "elsa.activities.controlflow/********", "hashPath": "elsa.activities.controlflow.********.nupkg.sha512"}, "Elsa.Activities.Email/********": {"type": "package", "serviceable": true, "sha512": "sha512-zzR6nVSCZqEjPXfPZShU4cjwD+cdX9x4Of3mR47OKjH61wv+Pk4rQSTxhvDjTOZYcZq6SvMgTONGhRmnJ8mGHw==", "path": "elsa.activities.email/********", "hashPath": "elsa.activities.email.********.nupkg.sha512"}, "Elsa.Activities.Http/********": {"type": "package", "serviceable": true, "sha512": "sha512-mkHhApQcJJVt3ejDAtXAdocY4HKgXcNz7a1Ws8fpIm+2S3lg40QHMYEnatximhcXkAmgOzEahen+NZBWE7tHrQ==", "path": "elsa.activities.http/********", "hashPath": "elsa.activities.http.********.nupkg.sha512"}, "Elsa.Activities.Timers/********": {"type": "package", "serviceable": true, "sha512": "sha512-JPRZ8yEwxEa77qZLAvofrnzo7H6BN0WpSr9hNM0jdL1++nFzqVOFK4vF80i4C4uDCgFLZuLFi6CwuxxqkCBJAw==", "path": "elsa.activities.timers/********", "hashPath": "elsa.activities.timers.********.nupkg.sha512"}, "Elsa.Activities.UserTask/********": {"type": "package", "serviceable": true, "sha512": "sha512-Ju0mGC6qOMshwGZV7CqTvaFsx/EKOOyNkiWdoPoJPeLyAdOoZXEcK1r4WSvUglBWxFVO1s/VWDY19tv81ODu4Q==", "path": "elsa.activities.usertask/********", "hashPath": "elsa.activities.usertask.********.nupkg.sha512"}, "Elsa.Activities.Workflows/********": {"type": "package", "serviceable": true, "sha512": "sha512-pAXwuWXF/Ni8QywQRQtpYZgdPkXFLJBXbtc/F57z+cDu2b4jHXiYiVqptrNwcJs+CIy+0ONhJGPNo7iRFzZhxw==", "path": "elsa.activities.workflows/********", "hashPath": "elsa.activities.workflows.********.nupkg.sha512"}, "Elsa.AutoMapper.Extensions/********": {"type": "package", "serviceable": true, "sha512": "sha512-7xW5yZeYgQJS5WUcxsK9S+PYm9TeU9gqIfci0/EOKtgmjHG4wtwvU0IGHuT0Ne8DkdC7Owhzwe5j1ER6SJfOrQ==", "path": "elsa.automapper.extensions/********", "hashPath": "elsa.automapper.extensions.********.nupkg.sha512"}, "Elsa.Core/********": {"type": "package", "serviceable": true, "sha512": "sha512-Z9b4sHJwvlqZD3u69LBELivW0+EeYV7wrA19c84ULMrkrmZu/bT/yw6Gplo+oH09dg1EeaxoXmdlOZo/SNz7vw==", "path": "elsa.core/********", "hashPath": "elsa.core.********.nupkg.sha512"}, "Elsa.Scripting.JavaScript/********": {"type": "package", "serviceable": true, "sha512": "sha512-AaZN1zVRR7iUuzLhgcaBmOD2SUhv8pWLCBPpmKTudHXjrqQEo1Vj+NlH5F7QBGENac89/5j8ai037j7D61IbTA==", "path": "elsa.scripting.javascript/********", "hashPath": "elsa.scripting.javascript.********.nupkg.sha512"}, "Elsa.Scripting.Liquid/********": {"type": "package", "serviceable": true, "sha512": "sha512-cIJLMqqhtNIDrj8vCILyO0y4kY+tj+8odkO13fyY5/IGLM8Z8zg0rcOSND56P24CeEJ23dqQgxzm7g/JmbqRlg==", "path": "elsa.scripting.liquid/********", "hashPath": "elsa.scripting.liquid.********.nupkg.sha512"}, "Elsa.Scrutor/********": {"type": "package", "serviceable": true, "sha512": "sha512-nPRCfiF/7HiwWWjO/UJmQ0dYhCDJoLJSIBplissc7TlnhEJreMwwowjHcbX6laeqkB4ZPb6pI4O8UY29wIr3cA==", "path": "elsa.scrutor/********", "hashPath": "elsa.scrutor.********.nupkg.sha512"}, "Esprima/1.0.1246": {"type": "package", "serviceable": true, "sha512": "sha512-6+NmPbDBcYJXpNm8+tA7OR3CtJkUHwkvdYBnON5ZEnUEDSdcEjkzt0kLDbGct9JfyxRbksvXBObAOWzqQt1XMw==", "path": "esprima/1.0.1246", "hashPath": "esprima.1.0.1246.nupkg.sha512"}, "Fluid.Core/1.0.0-beta-9605": {"type": "package", "serviceable": true, "sha512": "sha512-yYsdqopSUc60iYIhmeY+Q18QYR6GirWpkRRxx/vWxy+CtdnO1fobq1dUG0l5gGcHDEryxg8vFG+uT69dJTocbA==", "path": "fluid.core/1.0.0-beta-9605", "hashPath": "fluid.core.1.0.0-beta-9605.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "IdentityModel/6.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4AXZ6Tp+DNwrSSeBziiX/231i8ZpD77A9nEMyc68gLSCWG0kgWsIBeFquYcBebiIPkfB7GEXzCYuuLeR1QZJIQ==", "path": "identitymodel/6.2.0", "hashPath": "identitymodel.6.2.0.nupkg.sha512"}, "Irony.Core/1.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-cLyDKtxWRkLkYoh3xRtDtaNEr/Yu/wM52qVi0/0coOqGQPCWOO9XW0w0RrZhwv7ZhmrB5KdB3SGjnDG8eWTMiQ==", "path": "irony.core/1.0.7", "hashPath": "irony.core.1.0.7.nupkg.sha512"}, "JetBrains.Annotations/2023.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "path": "jetbrains.annotations/2023.3.0", "hashPath": "jetbrains.annotations.2023.3.0.nupkg.sha512"}, "Jint/3.0.0-beta-1629": {"type": "package", "serviceable": true, "sha512": "sha512-BB/8LdgQI1CVSZGa9t7AMe+TlUVYlhg4a41096gEEnw6XfKiLBnt0WtsrJLECHo5dH92fKttG6BWbevJwoCGwg==", "path": "jint/3.0.0-beta-1629", "hashPath": "jint.3.0.0-beta-1629.nupkg.sha512"}, "LibGit2Sharp/0.28.0": {"type": "package", "serviceable": true, "sha512": "sha512-+VGXLAQovtTc41EkUXBKSuu40XcyuWUmQrslpd0CPMGkpnLTgQwoRLSSCRxLSPjYSi9SskyRUOLa9tjg/L108A==", "path": "libgit2sharp/0.28.0", "hashPath": "libgit2sharp.0.28.0.nupkg.sha512"}, "LibGit2Sharp.NativeBinaries/2.0.320": {"type": "package", "serviceable": true, "sha512": "sha512-7vIqOhB+5LOi9arXi8IdlkWURpQEiMtnwVP//djA7cQvIVfpC4bZSnQIDe4kwwvsU/w1oH0YkBTgMmpkVndNbg==", "path": "libgit2sharp.nativebinaries/2.0.320", "hashPath": "libgit2sharp.nativebinaries.2.0.320.nupkg.sha512"}, "MailKit/2.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-txopK34kCTVfM8cGewvv2KZBsSuE8iXJ5xbdRa5dOIxw9SLjGzpyHiQvcTW8i3XQm70cBRhlBCpPy22qU35N+A==", "path": "mailkit/2.4.1", "hashPath": "mailkit.2.4.1.nupkg.sha512"}, "MediatR/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cYSDJ2b/dIiwTLW/ZW/SQcXq9I3WF6KR4D0KN4hxWR08tZqcvcVS1bQXZbsANdQdTQfxx+ZFp5WkHLMyeoR5Zw==", "path": "mediatr/8.0.0", "hashPath": "mediatr.8.0.0.nupkg.sha512"}, "MediatR.Extensions.Microsoft.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qGa+dbfrpk6y01swgc0uM+jG5tkb5xVeY1P4iByd3NQCDO7nmrMBnBRoMGhKlSigQ6iWXDiwOpzC01W4xrJxLg==", "path": "mediatr.extensions.microsoft.dependencyinjection/8.0.0", "hashPath": "mediatr.extensions.microsoft.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OGIGJMnlWvQgcweHcv1Mq/P24Zx/brUHeEdD05NzqkSXmQSnFomTvVyCuBtCXT4JPfv2m70y1RSocmd9bIbJRg==", "path": "microsoft.aspnetcore.authorization/8.0.0", "hashPath": "microsoft.aspnetcore.authorization.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sFfUegR52PSmTcT2eH1YywPdXLwx0Tt4S54yjqIP5+R/BmZKbEfCOvaGqAwKYbpwztHTua013pPeDKP8DmuGtw==", "path": "microsoft.aspnetcore.cryptography.internal/3.1.1", "hashPath": "microsoft.aspnetcore.cryptography.internal.3.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Pw5q8KrUw4O3ViciTnxtIdR8UsucGEqQdGGahSUNTu45C15+KhiITVJJREhjT2PyNnSbd8ywVTc0bCUfXsrDyg==", "path": "microsoft.aspnetcore.dataprotection/3.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.3.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yEPPh6+lH2y2UCti+2fHhUBT8/akf0g279KwvDN0DpM1UGEvxziSLKJM42YvaQrJRHevi0qXpczuHgstfNTP0g==", "path": "microsoft.aspnetcore.dataprotection.abstractions/3.1.1", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.3.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OmuSztiZMitRTYlbMNDkBk3BinSsVcOApSNBAsrw+KYNJh6ALarPhWLlKdtvMgrKzpyCY06xtLAjTmQLURHSlQ==", "path": "microsoft.aspnetcore.metadata/8.0.0", "hashPath": "microsoft.aspnetcore.metadata.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JjNCPx5Jbk7WTjaFI9Kyq+ZRVwMJ7Ee2AmgLKraQUS8AuciAO+euxHAhlhv0AWCsVyibyRr+5USxjyc7U6VsMA==", "path": "microsoft.extensions.dependencymodel/3.1.1", "hashPath": "microsoft.extensions.dependencymodel.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Ev1goLIvggLF6uCs6oZvdr9JM+2b1Zj+4FLdBWNW5iw3tm2BymVIb0yMsjnQTBWL7YUmqVWH3u45hSqOfvuqg==", "path": "microsoft.extensions.fileproviders.embedded/9.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tzf+x8HVDhri9dkIGCGOP5RVjgXnTZuBcwZHr0sW6KAtVGPDU0xEDXPDpoW5vwq/K12dMI75vuapUtMw0d/pIw==", "path": "microsoft.extensions.http.polly/5.0.1", "hashPath": "microsoft.extensions.http.polly.5.0.1.nupkg.sha512"}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "path": "microsoft.extensions.localization/8.0.0", "hashPath": "microsoft.extensions.localization.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "path": "microsoft.extensions.localization.abstractions/8.0.0", "hashPath": "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-KvAEmAv53lwNJhY5ARc+9jSrDq/61rWwsFUEX5wlFbfRPFPH9IvV/puZgW+uTDWV0hyqe0htTtT7QaqTiVxI6A==", "path": "microsoft.extensions.logging.configuration/3.1.1", "hashPath": "microsoft.extensions.logging.configuration.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-d05e3zmLepl57LQA7ZWGX71iG2PuyqBkqQZQ7wgZbVqf40TYz+f/zlO8UkgylEGnT3Hf1ZWCT8NDAPyqc5xqiQ==", "path": "microsoft.extensions.logging.console/3.1.1", "hashPath": "microsoft.extensions.logging.console.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-L50f1F9ZdvvBmGvdoUONsh6ScUtG+mP8TEYMHMKR3a0cSPYMHv28GnNPhbXY5zN0fr53So0I8SR2M02utn2zNQ==", "path": "microsoft.extensions.logging.debug/3.1.1", "hashPath": "microsoft.extensions.logging.debug.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "MimeKit/2.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-VykVWY4OOdv30IY1fmahWYR4FNtzX0Cr/SVh5NUqiTfsHMFBLqAnC7Tm3sWCuO32CYrs78yhEd3i8yTPMjKeYA==", "path": "mimekit/2.4.1", "hashPath": "mimekit.2.4.1.nupkg.sha512"}, "NCrontab/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-G3tzcIIgsiyZyVbHNPyn5+adaM9UjeVNgjrRsvXU7wo2sMhpvpQrir29dcjIND53H/fuTdgg9nI3SfFFg7barA==", "path": "ncrontab/3.3.1", "hashPath": "ncrontab.3.3.1.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "NodaTime/3.0.0-beta01": {"type": "package", "serviceable": true, "sha512": "sha512-6C9Pdzj2iZWroyl+gn2jfrl12B32PnhWz0IczPjBS+G7iseriRjoj0duwvZSHWaw7SUuw0K4kbPH9KUW8300YA==", "path": "nodatime/3.0.0-beta01", "hashPath": "nodatime.3.0.0-beta01.nupkg.sha512"}, "NodaTime.Serialization.JsonNet/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7Qd/rTe6B5dJbjMW2oODE5HrEgm8RRq7gMqcpu9sW29IVymynHnQtJL42AAkw81skA7LjLbJG7sevWqe1NH/Aw==", "path": "nodatime.serialization.jsonnet/2.2.0", "hashPath": "nodatime.serialization.jsonnet.2.2.0.nupkg.sha512"}, "NuGet.Common/6.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-GKFWxuDBcX9YWT6+IBNVVrnN0RA65U76DPllr9bYGv3WZ7xy420qeZDCcLfsFSGImJ0yPX55DGotSTIyWrDC/g==", "path": "nuget.common/6.2.2", "hashPath": "nuget.common.6.2.2.nupkg.sha512"}, "NuGet.Configuration/6.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-HMsMLI2zBwpvAArZMHuyt5DO+lhXUcqFGC3GQj2Ykvbn7kzxZdbsBBpAKMpLT6DhYEhYdahTiDwe2cjchSvv4w==", "path": "nuget.configuration/6.2.2", "hashPath": "nuget.configuration.6.2.2.nupkg.sha512"}, "NuGet.Frameworks/6.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-U+Ax+WbQTDzldYU7EWDB/SPDmQpYleK6I9mohdADyCTBzCLwVBJvt3CIexbhxctOYS8aeHkWZE58YaWOVOC4jA==", "path": "nuget.frameworks/6.2.2", "hashPath": "nuget.frameworks.6.2.2.nupkg.sha512"}, "NuGet.Packaging/6.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-016aapXsWeKyhxEH+CVUbpvz492nSCB+Rt+q9SDbEBBhAcWvcx6noOxoplHvhfxLg2adlpuNFIL3PSGO6krxFg==", "path": "nuget.packaging/6.2.2", "hashPath": "nuget.packaging.6.2.2.nupkg.sha512"}, "NuGet.Protocol/6.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-HAhpbgwwauffx8aBxPbhm/RcsLBKwBgJ+8tg6jXSiuWehEzo57EAkKNrUulhVOHQZyZVWC/zL0uhRjaUv6RltQ==", "path": "nuget.protocol/6.2.2", "hashPath": "nuget.protocol.6.2.2.nupkg.sha512"}, "NuGet.Versioning/6.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-3sKMpt6btwpv6TKbbpUisT7a9qZoqoAGvHC0lUiqMl9V1oArqXP0DRhlNq7alFynA1HqKOFeRje5kPYkbqFJ/Q==", "path": "nuget.versioning/6.7.0", "hashPath": "nuget.versioning.6.7.0.nupkg.sha512"}, "NUglify/1.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-9J44o89PRdcS4GOyj0obkLfjCAuJItI4FrNmwALkjRKlzvHVlTB2ALbC9aigIoCMqzy0Xlc0mIVD/jO9WVDHiA==", "path": "nuglify/1.21.0", "hashPath": "nuglify.1.21.0.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Polly/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-KZm8iG29y6Mse7YntYYJSf5fGWuhYLliWgZaG/8NcuXS4gN7SPdtPYpjCxQlHqxvMGubkWVrGp3MvUaI7SkyKA==", "path": "polly/8.2.0", "hashPath": "polly.8.2.0.nupkg.sha512"}, "Polly.Core/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gnKp3+mxGFmkFs4eHcD9aex0JOF8zS1Y18c2A5ckXXTVqbs6XLcDyLKgSa/mUFqAnH3mn9+uVIM0RhAec/d3kA==", "path": "polly.core/8.2.0", "hashPath": "polly.core.8.2.0.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Portable.BouncyCastle/1.8.5": {"type": "package", "serviceable": true, "sha512": "sha512-EaCgmntbH1sOzemRTqyXSqYjB6pLH7VCYHhhDYZ59guHSD5qPwhIYa7kfy0QUlmTRt9IXhaXdFhNuBUArp70Ng==", "path": "portable.bouncycastle/1.8.5", "hashPath": "portable.bouncycastle.1.8.5.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M2nN92ePS8BgQ2oi6Jj3PlTUzadYSIWLdZrHY1n1ZcW9o4wAQQ6W+aQ2lfq1ysZQfVCgDwY58alUdowrzezztg==", "path": "runtime.native.system.net.security/4.3.0", "hashPath": "runtime.native.system.net.security.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "Scriban/5.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-qk2W8wQpm7mykWrEi9fhjC7uVapT2bkweMuMqebrF7gaVMt0WjmZzyVVTpom5cUsc3ddMDpo95SkNcTWGo+L6Q==", "path": "scriban/5.10.0", "hashPath": "scriban.5.10.0.nupkg.sha512"}, "Serilog/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jDkUrSh5EofOp7Lx5Zgy0EB+7hXjjxE2ktTb1WVQmU00lDACR2TdROGKU0K1pDTBSJBN1PqgYpgOZF8mL7NJw==", "path": "serilog/4.0.0", "hashPath": "serilog.4.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "StackExchange.Redis/2.7.4": {"type": "package", "serviceable": true, "sha512": "sha512-lD6a0lGOCyV9iuvObnzStL74EDWAyK31w6lS+Md9gIz1eP4U6KChDIflzTdtrktxlvVkeOvPtkaYOcm4qjbHSw==", "path": "stackexchange.redis/2.7.4", "hashPath": "stackexchange.redis.2.7.4.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.CommandLine/2.0.0-beta1.20371.2": {"type": "package", "serviceable": true, "sha512": "sha512-L6cnw4QgaLAOpUPSW1TjYGJGKsUvyDYvdQ/Gqv0/EnOSzByEf+IlAK1sRu+bcAhBrdws+eNPcOarg1Qj33oNDA==", "path": "system.commandline/2.0.0-beta1.20371.2", "hashPath": "system.commandline.2.0.0-beta1.20371.2.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "path": "system.linq.dynamic.core/1.3.5", "hashPath": "system.linq.dynamic.core.1.3.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Security/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xT2jbYpbBo3ha87rViHoTA6WdvqOAW37drmqyx/6LD8p7HEPT2qgdxoimRzWtPg8Jh4X5G9BV2seeTv4x6FYlA==", "path": "system.net.security/4.3.2", "hashPath": "system.net.security.4.3.2.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "path": "system.reflection.metadata/6.0.1", "hashPath": "system.reflection.metadata.6.0.1.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9TPLGjBCGKmNvG8pjwPeuYy0SMVmGZRwlTZvyPHDbYv/DRkoeumJdfumaaDNQzVGMEmbWtg07zUpSW9q70IlDQ==", "path": "system.security.cryptography.pkcs/5.0.0", "hashPath": "system.security.cryptography.pkcs.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cJV7ScGW7EhatRsjehfvvYVBvtiSMKgN8bOVI0bQhnF5bU7vnHVIsH49Kva7i7GWaWYvmEzkYVk1TC+gZYBEog==", "path": "system.security.cryptography.protecteddata/4.4.0", "hashPath": "system.security.cryptography.protecteddata.4.4.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-B6pAyxMvXGbZemb+ER877KSr6OKis+qAdxhhKKK36I6sgj2js8mbcEVviZEHYV8XRTWjbKsAq8Z/zoaegA30dA==", "path": "system.security.cryptography.xml/4.7.0", "hashPath": "system.security.cryptography.xml.4.7.0.nupkg.sha512"}, "System.Security.Permissions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "path": "system.security.permissions/8.0.0", "hashPath": "system.security.permissions.8.0.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "path": "system.threading.tasks.extensions/4.5.2", "hashPath": "system.threading.tasks.extensions.4.5.2.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "path": "system.windows.extensions/8.0.0", "hashPath": "system.windows.extensions.8.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "TimeZoneConverter/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "path": "timezoneconverter/6.1.0", "hashPath": "timezoneconverter.6.1.0.nupkg.sha512"}, "Volo.Abp.Account.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-9uNJfv9fM//daZoS8eF/XgcTfFn8zYHcpd9fr/TMgfXvHnMDmtKRNRkIga0yuSNa3K4IDvtE501XR2FPOCCIug==", "path": "volo.abp.account.application.contracts/8.1.1", "hashPath": "volo.abp.account.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.Auditing/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ft29kbMs2t07jApe3y9e2f9uExIigjIbPzaKkV1SuvdVyVn3TUiTNylrAsZB9WU6WDGgbQSz29/W3MC4EWSeOQ==", "path": "volo.abp.auditing/8.1.1", "hashPath": "volo.abp.auditing.8.1.1.nupkg.sha512"}, "Volo.Abp.Auditing.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-xUk7lOZxcJoQBjPCIiYxZO2D2tavHXpYCIwH3SfG4xOOwb4ro1TqBKCPMki9ermX0yKmaL2eM55wsXT7RRxuLg==", "path": "volo.abp.auditing.contracts/8.1.1", "hashPath": "volo.abp.auditing.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.Authorization/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-O/SsMfTekNmsP6+jP4So1BY3bPyEGDLpwxD4frLAea+/zVg6c2ENldKjAOutTQJX2TcCpD4w7XEZAVhQ+HTd9Q==", "path": "volo.abp.authorization/8.1.1", "hashPath": "volo.abp.authorization.8.1.1.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dBZjyo3xXwn/XFgzIfUk4W8sICOsCKbhk1Ug4n5a8KPG17fcoovFFug4ACcY80TMGsrgQ8TaibZsNRgLXPHnQw==", "path": "volo.abp.authorization.abstractions/8.1.1", "hashPath": "volo.abp.authorization.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Autofac/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-B326MyX1e9dKPl3nfHswc7iqRgkhLH3u9WeUrc7pnAmMQwZXvnXsk3BF8xy+f/500ab+iJMrsuLXwv8NhMg9hA==", "path": "volo.abp.autofac/8.1.1", "hashPath": "volo.abp.autofac.8.1.1.nupkg.sha512"}, "Volo.Abp.AutoMapper/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-QHkTzeu8kwVodZZwceMGONW56xmdR5fwBTPO1lrQCbKy7i2AfLos5LfEaBFiq9aHCSPWUQx9dU0IISZTdKpW9A==", "path": "volo.abp.automapper/8.1.1", "hashPath": "volo.abp.automapper.8.1.1.nupkg.sha512"}, "Volo.Abp.BackgroundWorkers/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-86+vkzHDSKio95VO93bNmpuuRe+usPuQDw+IoRQbwI/4raqFoSyiCGKvGOp/Fnm4pIIutFvqBPpmloexpHdWig==", "path": "volo.abp.backgroundworkers/8.1.1", "hashPath": "volo.abp.backgroundworkers.8.1.1.nupkg.sha512"}, "Volo.Abp.Caching/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-C+GEspeakR12qItw0QVQah+e4p5TPSPe4My5CyPIrzZwN5YKa3pEYvytjGpGvQ4+hcbPkHh5HN2lxLg44G0acQ==", "path": "volo.abp.caching/8.1.1", "hashPath": "volo.abp.caching.8.1.1.nupkg.sha512"}, "Volo.Abp.Castle.Core/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-0IRz2H9bWTfl5vNzVxnzUx0tx2q9/YpAgKfX7L5cm5YZSY/S2Cwx5VMjNpVxG9uFf6CtPMMTWWqTF5JyW83YVg==", "path": "volo.abp.castle.core/8.1.1", "hashPath": "volo.abp.castle.core.8.1.1.nupkg.sha512"}, "Volo.Abp.Cli.Core/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-T2fQt9f95kT/86v94ntf/9KCYhp6O/KrUpWsKdM1deJ0VvXjhGtuGe3NV+q9dkBDbb3GtVRgEDN+oYpmCxDMKw==", "path": "volo.abp.cli.core/8.1.1", "hashPath": "volo.abp.cli.core.8.1.1.nupkg.sha512"}, "Volo.Abp.Core/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-sNtdDe2/EWSz5tyHHWTJv8s2znVHVEP3019F6vXkoPYeL3kNYjSMX1W+ibH/TP5jmowaRZc9AZbfjHUb2bnHkQ==", "path": "volo.abp.core/8.1.1", "hashPath": "volo.abp.core.8.1.1.nupkg.sha512"}, "Volo.Abp.Data/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-FkGUAlgMFel85sUAVLBSI2K8nt9eEbyZCp9GT8ZVxGLKHnvUNfotefb7MJZSYSpwpmA1Xg8REkd2IQsqsGgzbw==", "path": "volo.abp.data/8.1.1", "hashPath": "volo.abp.data.8.1.1.nupkg.sha512"}, "Volo.Abp.Ddd.Application/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-3bXSCyITr/jgJ8vXq6/zwcvMcX/ifi8s2lvEa6dnUkomp/XlohmY0NahYUbgIn6P0Gj5WeFzcQOa32YttoJxfA==", "path": "volo.abp.ddd.application/8.1.1", "hashPath": "volo.abp.ddd.application.8.1.1.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-nHy1J2jvP9WxIl5gZBLjQt+oK5DfKVuKDLaLpimiC7LNiRI4vLMyCe1YWJQmSmtMd9QYNpAKCmoBsAB5X1IR4g==", "path": "volo.abp.ddd.application.contracts/8.1.1", "hashPath": "volo.abp.ddd.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.Ddd.Domain/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-M+V9JrSUpwD0Es/2mM0CcIox5iT3c4dLgJghignnKRXl41YvhVcWllh/rezFoXCVqgRVSe3212zlQaCsyq5ZCw==", "path": "volo.abp.ddd.domain/8.1.1", "hashPath": "volo.abp.ddd.domain.8.1.1.nupkg.sha512"}, "Volo.Abp.Ddd.Domain.Shared/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y4BBDgdwHtGBds5Ek0Tz84a361uMdI4x5OVqnLphnOqbphHB74KoyJ8uOET8spX9SolhWM57gLNSnVT7tkg5qA==", "path": "volo.abp.ddd.domain.shared/8.1.1", "hashPath": "volo.abp.ddd.domain.shared.8.1.1.nupkg.sha512"}, "Volo.Abp.DistributedLocking.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tdnb/kPLbDmmU5ullLX//3gIBAJ0Et7OuYADydW6a22NHwHsxPhs9w++UEsjkxFKz+0Fy97MkJLOt65xcNjAZA==", "path": "volo.abp.distributedlocking.abstractions/8.1.1", "hashPath": "volo.abp.distributedlocking.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.EventBus/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h/SrkDEK6t54x4A6HnRzNqco+5oB5rf8V8rRJExFDgUxte+OuAEneDcFUm41HtOkIXgDKQiAeYHSDoIe63OnIQ==", "path": "volo.abp.eventbus/8.1.1", "hashPath": "volo.abp.eventbus.8.1.1.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CX13/KxRTEJ9aio/dQd/gtTy00pq8f93p4YNbrmvhAuw9NpWc4rRPuNkRdm7TkpvXX2rezQBzNEZYzanB41KIA==", "path": "volo.abp.eventbus.abstractions/8.1.1", "hashPath": "volo.abp.eventbus.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-A<PERSON>hRYR5THspYRyLFc7lnLPNQkI0Bn4j3tcsXJAiwpQkvPmivLgOL66C9gGBuM42Q9v6Tk6Y6RIy/G70pJbKc9g==", "path": "volo.abp.exceptionhandling/8.1.1", "hashPath": "volo.abp.exceptionhandling.8.1.1.nupkg.sha512"}, "Volo.Abp.Features/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-bMvjYmnxW4NTWwBgGjafSuIBgVdGkwy2xSO0ZIqbcxXTubnnw1C8aiOTZzg+pTobTfkJPU4GdimyRchcq+K3Jg==", "path": "volo.abp.features/8.1.1", "hashPath": "volo.abp.features.8.1.1.nupkg.sha512"}, "Volo.Abp.GlobalFeatures/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZjzC2sSs1IJey58HRdxI5EBTBA098LHuJq/EHrwrGppPFwJhFUyqoypsNsY0dqyWxlj7EY5+yAS5TlMXgM78Bw==", "path": "volo.abp.globalfeatures/8.1.1", "hashPath": "volo.abp.globalfeatures.8.1.1.nupkg.sha512"}, "Volo.Abp.Guids/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-3nnNnffKkIKrzl14xlhaPads4GVzPneFSf59w+wKkzTjnbC6cmsWmC+6W7qcVAYFpwioXUpBQLMZj1BQgREwew==", "path": "volo.abp.guids/8.1.1", "hashPath": "volo.abp.guids.8.1.1.nupkg.sha512"}, "Volo.Abp.Http/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PLhOHstLHOtdr03wXVod+mi7MoxKhOJ1RCjJqk71XlGQWLl4YhdcXfrG+rH3+vkRkAn4XyYa/n50InmqemTdZg==", "path": "volo.abp.http/8.1.1", "hashPath": "volo.abp.http.8.1.1.nupkg.sha512"}, "Volo.Abp.Http.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-b2cxbkFPm3tqb7g7xXfpedRnjuuOHytlpLsdWlQ5rS5sWig/3f6uxFSRDoh9DJU34YGa5Gctd6I7IaSYvdMWog==", "path": "volo.abp.http.abstractions/8.1.1", "hashPath": "volo.abp.http.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Identity.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-BBhSossoDSC2Iq24l4wghHT0Mu+PHByXXKR7j/HczSMD0Y8oV9XQXNscUOavNznp3VXFi9avuq5xqNGqcpIuuQ==", "path": "volo.abp.identity.application.contracts/8.1.1", "hashPath": "volo.abp.identity.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+lvLGuzrwCGQBsx6od6gK/2x5nL6szg6AOS63MtlOn3ZFSDZbkreUSoi2H/AKNLAgN5NPBjwLNEWdAgPu6t2Lw==", "path": "volo.abp.identity.domain.shared/8.1.1", "hashPath": "volo.abp.identity.domain.shared.8.1.1.nupkg.sha512"}, "Volo.Abp.IdentityModel/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-5GNGT/i435qPekJdBKoDd8Y2wx/h35vByRbsKeN8EcbfsOCRe6oPQftzC/ElqCaGRfpExb7fkLaO0CrElIl9Cg==", "path": "volo.abp.identitymodel/8.1.1", "hashPath": "volo.abp.identitymodel.8.1.1.nupkg.sha512"}, "Volo.Abp.Json/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PIEnmql9AbeGTph1kOhCnUqB+f00iFaujFwxp68Cx/5d3D7PO5zpzIgLVlASV29++BTmbMZaSzNctEozj0pdrg==", "path": "volo.abp.json/8.1.1", "hashPath": "volo.abp.json.8.1.1.nupkg.sha512"}, "Volo.Abp.Json.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-vn4bXF9KWkcKOz3MjOrn3blA1GAxd0ePCBW29LQHg6GKTUuzvhcV75vNb5NdspErDBbXSL8tY0ujr9/kvCmD+A==", "path": "volo.abp.json.abstractions/8.1.1", "hashPath": "volo.abp.json.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Json.SystemTextJson/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-xtnaPqGReRFFTTiGy5QaMB3kNqd2Ydz9VTqrGouLvc1AaqUF9+KhOOCh9kVA18HEqESrm5VxKqpS2ZvRnEEHvw==", "path": "volo.abp.json.systemtextjson/8.1.1", "hashPath": "volo.abp.json.systemtextjson.8.1.1.nupkg.sha512"}, "Volo.Abp.Localization/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-QECnrtfqsPIlVYHVsa4nilgVTnQbEqmOEjd2gD0oKOEdXQd7iIAhYMHeTbvSO0nwXkq9FztAf2qmCRMDtrbUjw==", "path": "volo.abp.localization/8.1.1", "hashPath": "volo.abp.localization.8.1.1.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Or7NaSIj0wizPtguGUeWW7stoaXUpGr61Y3MRtYjmjIGFxAcrIuX3drlwGK9aB5m6Xt5xQNVkqh4Yy8kEkmNVQ==", "path": "volo.abp.localization.abstractions/8.1.1", "hashPath": "volo.abp.localization.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Minify/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jigvjlYMCjMM1nb6BfB+/j7fh0YUwIriV6hZfX5/7prIwObt5DoxqLwMtzhjaE+GVbAVT3LzUWPd/JZMLHntHA==", "path": "volo.abp.minify/8.1.1", "hashPath": "volo.abp.minify.8.1.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-KRh2YuKoFVwUhe0bX6If+ukXGLnez0IudjocwwUTLJiBZ1IWjMb70OBbEy03LunIuvft/3i5m7uwIqdDBYKeVA==", "path": "volo.abp.multitenancy/8.1.1", "hashPath": "volo.abp.multitenancy.8.1.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vvm4oQsq+uRe29VgUEDiCe8eb2ML2dppcc7YNM1tiiNrDAUBG4lYSiBBvUUABXzS+jS/P1zmkEieU07CFaQSqw==", "path": "volo.abp.multitenancy.abstractions/8.1.1", "hashPath": "volo.abp.multitenancy.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.ObjectExtending/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0K8/fu1xpXcWl2CBCHvf8JP5t9wMt9YMlCjGzaGKP2Y/WQMWtN41yyEeKxYdKCnfcLQ8ZNffZQRPAX6hQKCkQ==", "path": "volo.abp.objectextending/8.1.1", "hashPath": "volo.abp.objectextending.8.1.1.nupkg.sha512"}, "Volo.Abp.ObjectMapping/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-8+MT2rY+GaLCc7NsJz4OHy8KiqSqVna3LY+U6Wtg2xPbRnAumUWT/rgOXOLByrplhv40flqw0O77q1yLAO2SJw==", "path": "volo.abp.objectmapping/8.1.1", "hashPath": "volo.abp.objectmapping.8.1.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Application.Contracts/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-YUerxRU7UQptEGqaBbQ+Kx2Vxdm/bYUn/K07vNuNq9xSwLXzDocMPO6taGKZ1oEs8o0Nnmq/QgKo7f5u7bB4qQ==", "path": "volo.abp.permissionmanagement.application.contracts/8.1.1", "hashPath": "volo.abp.permissionmanagement.application.contracts.8.1.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Shared/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-/dXxzRCbr+WMAuuoTc0KrJh4b/JIXtGpkI8nnsHOg5LdzmMF84AnNEM7ZwssQm0slsEJzhfjDMuUrEK2aWRhVw==", "path": "volo.abp.permissionmanagement.domain.shared/8.1.1", "hashPath": "volo.abp.permissionmanagement.domain.shared.8.1.1.nupkg.sha512"}, "Volo.Abp.Security/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UfC7k+F5U7mj1uBwBEfbGAbjbyeuJXxPLbYtlEPqBztXBGM9VnaQ2/gLN3vdgfJiU0TDx6fmbZJm5lCzVclGyQ==", "path": "volo.abp.security/8.1.1", "hashPath": "volo.abp.security.8.1.1.nupkg.sha512"}, "Volo.Abp.Serialization/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-dNm7frlMg66PckzHtlXqu3ow7ItWcC1NqE+WKT6NCuSC6MEJhiJj5phn9VzKgjbOUYtjlNVbO1QIAYOoVd1AYg==", "path": "volo.abp.serialization/8.1.1", "hashPath": "volo.abp.serialization.8.1.1.nupkg.sha512"}, "Volo.Abp.Settings/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UT+BsKnvB2y35QCi5+TtFekw66/j8o8GelLLWOwf99uqfi3Xk9ntSRtmvXP/vDij9Kyv/ReGWg/E/kwwqQIblg==", "path": "volo.abp.settings/8.1.1", "hashPath": "volo.abp.settings.8.1.1.nupkg.sha512"}, "Volo.Abp.Specifications/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yFTgoNieABKRz19KLR+mIQmS5iRgaE/bbPZVyoGtyr83aLCH7Y84svt1Wpzn7AsOwMXm8njw8I0UEIw5wj6hFQ==", "path": "volo.abp.specifications/8.1.1", "hashPath": "volo.abp.specifications.8.1.1.nupkg.sha512"}, "Volo.Abp.Threading/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-mMUfVsOoHE5c7CgYQKacZZoEV7+i0HepXUxiBjUfS+mwAu6KS/SDUN9KMFWtRtIbqYycRObTF+LdAQFr8EDq0Q==", "path": "volo.abp.threading/8.1.1", "hashPath": "volo.abp.threading.8.1.1.nupkg.sha512"}, "Volo.Abp.Timing/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+qmruseshbQ2utykpTu/czIAs9kWWS6FMWkV2/nw98/LYMppveX3Wz7TO9t6QsDCWPXOgvtAOU2xd0IB/aiOlw==", "path": "volo.abp.timing/8.1.1", "hashPath": "volo.abp.timing.8.1.1.nupkg.sha512"}, "Volo.Abp.Uow/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Jjg0UFya+vJ5omveIuSbXivFqbNuklsUgydpySgRntGMQt4wBxV04ZlqjxzjDi7uCQ8CkRwaxVZNtbIXgdoTbg==", "path": "volo.abp.uow/8.1.1", "hashPath": "volo.abp.uow.8.1.1.nupkg.sha512"}, "Volo.Abp.Users.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-FIRoheeC7EnfDYi6LQfo4Ud50I5T3zJ+UTZqG+7J1akOBaN1FOenAoRwS54SghdRQ3Ti3sIESe2cW79ywc25fg==", "path": "volo.abp.users.abstractions/8.1.1", "hashPath": "volo.abp.users.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6Kcg36g0li48oRfMw/InqimhFCxydSokZJ0r07fwQgUtSWJ0VsHLUpS0ZlyU0e/X9gK3PMLhlEZMGX6AQs36g==", "path": "volo.abp.users.domain.shared/8.1.1", "hashPath": "volo.abp.users.domain.shared.8.1.1.nupkg.sha512"}, "Volo.Abp.Validation/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ecqXxmkppJxkZ+NkSAqSgDxCoQ5UeQg3AlFJDwb4W13WRHqsS5R4uvvxMvsYdAz87uxOjAieZm+KE3dFtBX6hQ==", "path": "volo.abp.validation/8.1.1", "hashPath": "volo.abp.validation.8.1.1.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lyYP+IY6MGvrCOXM5DPZ3GI4iH3Xc+oXYPzi6kmRrFov8NSYUxCLYfnE2eNC3MqFC2Dw2JUmtumaVJ0g+t/OBQ==", "path": "volo.abp.validation.abstractions/8.1.1", "hashPath": "volo.abp.validation.abstractions.8.1.1.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/8.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-K0kwo4iaxVGro7Av3+wot+oIMqzwcC+xRjCODjznTGMH5d2Mao9WMSonQK3sphSkH7q5kukyKwS1VwS+V0Pg1w==", "path": "volo.abp.virtualfilesystem/8.1.1", "hashPath": "volo.abp.virtualfilesystem.8.1.1.nupkg.sha512"}, "YamlDotNet/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ib8lKt7TlODREDFLOWef0OmxChsQlTsRSGGx7A6YH8d7Y+YrMg8Z/RneW5WAnDsfygg/Ao2r2ND1UNe0LLPMFw==", "path": "yamldotnet/8.0.0", "hashPath": "yamldotnet.8.0.0.nupkg.sha512"}, "AbpTools.AbpHelper.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AbpTools.AbpHelper.Gui.Application.Contracts/2.16.0": {"type": "project", "serviceable": false, "sha512": ""}, "AbpTools.AbpHelper.Core.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}