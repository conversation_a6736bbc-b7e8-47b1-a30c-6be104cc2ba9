﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.GetSource;
using AbpTools.AbpHelper.Gui.AbpCli.GetSource.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;

namespace AbpTools.AbpHelper.Gui.Controllers.AbpCli
{
    [RemoteService]
    [Route("/api/abp-helper/abp-cli/get-source")]
    public class AbpCliGetSourceController : GuiController, IAbpCliGetSourceAppService
    {
        private readonly IAbpCliGetSourceAppService _service;

        public AbpCliGetSourceController(IAbpCliGetSourceAppService service)
        {
            _service = service;
        }
        
        [HttpPost]
        public virtual Task<ServiceExecutionResult> GetSourceAsync(AbpGetSourceInput input)
        {
            return _service.GetSourceAsync(input);
        }
    }
}