﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Update;
using AbpTools.AbpHelper.Gui.AbpCli.Update.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;

namespace AbpTools.AbpHelper.Gui.Controllers.AbpCli
{
    [RemoteService]
    [Route("/api/abp-helper/abp-cli/update")]
    public class AbpCliUpdateController : GuiController, IAbpCliUpdateAppService
    {
        private readonly IAbpCliUpdateAppService _service;

        public AbpCliUpdateController(IAbpCliUpdateAppService service)
        {
            _service = service;
        }
        
        [HttpPost]
        [Route("update")]
        public virtual Task<ServiceExecutionResult> UpdateAsync(AbpUpdateInput input)
        {
            return _service.UpdateAsync(input);
        }
    }
}