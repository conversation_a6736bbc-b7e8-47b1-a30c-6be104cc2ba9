﻿@using Volo.Abp.AspNetCore.Components.Web
@using AbpTools.AbpHelper.Gui.Localization
@using AbpTools.AbpHelper.Gui.CodeGeneration.Migration.Dtos;
@using AbpTools.AbpHelper.Gui.Blazor.Pages.Shared;
@inherits ExecutableComponentBaseWithDirectory<AbpHelperGenerateMigrationAddInput>
@inject AbpBlazorMessageLocalizerHelper<GuiResource> Lh

<Card Class="mb-4">
    <CardBody>
        <Alert Color="Color.Info"  Visible>
            <AlertDescription>
                <Icon Name="IconName.QuestionCircle" /> @L["Info:FunctionIsBasedOnAbpHelperPart1"]
            </AlertDescription>
            <AlertMessage>
                <Icon Name="IconName.ExternalLinkSquareAlt"/>
                <Link To="https://github.com/EasyAbp/AbpHelper.CLI/blob/develop/src/AbpHelper.Core/Commands/Ef/Migrations/Add/AddCommandOption.cs" Target="Target.Blank" Style="text-decoration: none">
                    @L["Info:FunctionIsBasedOnAbpHelperPart2_SourceCode"]
                </Link>
            </AlertMessage>
            <AlertDescription>
                @L["Info:FunctionIsBasedOnAbpHelperPart3"]
            </AlertDescription>
        </Alert>
        <Form>
            <Validations @ref="ValidationsRef" Model="Input" ValidateOnLoad="false">
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["Directory"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Directory" Autofocus="true">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["CodeGeneration_Migration:Name"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Name" Placeholder="e.g. Added_Product_Entity">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["CodeGeneration_Migration:EfOptions"]</FieldLabel>
                        <TextEdit @bind-Text="Input.EfOptions">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["CodeGeneration_Migration:MigrationProjectName"]</FieldLabel>
                        <TextEdit @bind-Text="Input.MigrationProjectName">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["ProjectName"]</FieldLabel>
                        <TextEdit @bind-Text="Input.ProjectName">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>
                <Validation MessageLocalizer="@Lh.Localize">
                    <Field>
                        <FieldLabel>@L["Exclude"]</FieldLabel>
                        <TextEdit @bind-Text="Input.Exclude">
                            <Feedback>
                                <ValidationError/>
                            </Feedback>
                        </TextEdit>
                    </Field>
                </Validation>

                <SubmitButton Block="true" Clicked="@ExecuteAsync" SaveResourceKey="Button:Execute" />
            </Validations>
        </Form>
    </CardBody>
</Card>