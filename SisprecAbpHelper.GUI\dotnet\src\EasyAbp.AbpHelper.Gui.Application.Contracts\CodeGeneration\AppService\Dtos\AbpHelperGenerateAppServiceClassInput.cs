﻿using System;
using System.ComponentModel.DataAnnotations;
using AbpTools.AbpHelper.Gui.CodeGeneration.Shared.Dtos;
using JetBrains.Annotations;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos
{
    [Serializable]
    public class AbpHelperGenerateAppServiceClassInput : AbpHelperGenerateInput
    {
        [Required]
        [NotNull]
        public virtual string Name { get; set; }

        [CanBeNull]
        public virtual string Folder { get; set; }


        public AbpHelperGenerateAppServiceClassInput()
        {
        }

        public AbpHelperGenerateAppServiceClassInput([NotNull] string directory, [CanBeNull] string projectName,
            [CanBeNull] string exclude, bool noOverwrite, [NotNull] string name, [CanBeNull] string folder) : base(
            directory, projectName, exclude, noOverwrite)
        {
            Name = name;
            Folder = folder;
        }
    }
}