﻿using System.Collections.Generic;
using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.Solutions;
using AbpTools.AbpHelper.Gui.Solutions.Dtos;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.Application.Dtos;

namespace AbpTools.AbpHelper.Gui.Controllers.Solutions
{
    [RemoteService]
    [Route("/api/abp-helper/solutions")]
    public class SolutionController : GuiController, ISolutionAppService
    {
        private readonly ISolutionAppService _service;

        public SolutionController(ISolutionAppService service)
        {
            _service = service;
        }
        
        [HttpGet]
        public virtual Task<ListResultDto<SolutionDto>> GetListAsync()
        {
            return _service.GetListAsync();
        }

        [HttpPost]
        [Route("use")]
        public virtual Task<SolutionDto> UseAsync(SolutionDto input)
        {
            return _service.UseAsync(input);
        }

        [HttpDelete]
        public virtual Task DeleteAsync(SolutionDto input)
        {
            return _service.DeleteAsync(input);
        }

        [HttpGet]
        [Route("packages")]
        public virtual Task<GetPackageDictionaryOutput> GetPackageDictionaryAsync(GetPackageDictionaryInput input)
        {
            return _service.GetPackageDictionaryAsync(input);
        }
    }
}