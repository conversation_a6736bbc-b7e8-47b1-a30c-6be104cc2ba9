C:\Users\<USER>\.nuget\packages\autofac\8.0.0\lib\net8.0\Autofac.dll
C:\Users\<USER>\.nuget\packages\autofac.extensions.dependencyinjection\9.0.0\lib\net8.0\Autofac.Extensions.DependencyInjection.dll
C:\Users\<USER>\.nuget\packages\autofac.extras.dynamicproxy\7.1.0\lib\net6.0\Autofac.Extras.DynamicProxy.dll
C:\Users\<USER>\.nuget\packages\automapper\9.0.0\lib\netstandard2.0\AutoMapper.dll
C:\Users\<USER>\.nuget\packages\bogus\35.5.1\lib\net6.0\Bogus.dll
C:\Users\<USER>\.nuget\packages\castle.core\5.1.1\lib\net6.0\Castle.Core.dll
C:\Users\<USER>\.nuget\packages\castle.core.asyncinterceptor\2.1.0\lib\net6.0\Castle.Core.AsyncInterceptor.dll
C:\Users\<USER>\.nuget\packages\elsa\********\lib\netstandard2.0\Elsa.dll
C:\Users\<USER>\.nuget\packages\elsa.abstractions\********\lib\netstandard2.0\Elsa.Abstractions.dll
C:\Users\<USER>\.nuget\packages\elsa.activities.console\********\lib\netstandard2.0\Elsa.Activities.Console.dll
C:\Users\<USER>\.nuget\packages\elsa.activities.controlflow\********\lib\netstandard2.0\Elsa.Activities.ControlFlow.dll
C:\Users\<USER>\.nuget\packages\elsa.activities.email\********\lib\netstandard2.0\Elsa.Activities.Email.dll
C:\Users\<USER>\.nuget\packages\elsa.activities.http\********\lib\netstandard2.0\Elsa.Activities.Http.dll
C:\Users\<USER>\.nuget\packages\elsa.activities.timers\********\lib\netstandard2.0\Elsa.Activities.Timers.dll
C:\Users\<USER>\.nuget\packages\elsa.activities.usertask\********\lib\netstandard2.0\Elsa.Activities.UserTask.dll
C:\Users\<USER>\.nuget\packages\elsa.activities.workflows\********\lib\netstandard2.0\Elsa.Activities.Workflows.dll
C:\Users\<USER>\.nuget\packages\elsa.automapper.extensions\********\lib\netstandard2.0\Elsa.AutoMapper.Extensions.dll
C:\Users\<USER>\.nuget\packages\elsa.core\********\lib\netstandard2.0\Elsa.Core.dll
C:\Users\<USER>\.nuget\packages\elsa.scripting.javascript\********\lib\netstandard2.0\Elsa.Scripting.JavaScript.dll
C:\Users\<USER>\.nuget\packages\elsa.scripting.liquid\********\lib\netstandard2.0\Elsa.Scripting.Liquid.dll
C:\Users\<USER>\.nuget\packages\elsa.scrutor\********\lib\netstandard2.0\Elsa.Scrutor.dll
C:\Users\<USER>\.nuget\packages\esprima\1.0.1246\lib\netstandard2.0\Esprima.dll
C:\Users\<USER>\.nuget\packages\fluid.core\1.0.0-beta-9605\lib\netstandard2.1\Fluid.dll
C:\Users\<USER>\.nuget\packages\humanizer.core\2.14.1\lib\net6.0\Humanizer.dll
C:\Users\<USER>\.nuget\packages\irony.core\1.0.7\lib\netstandard1.6\Irony.dll
C:\Users\<USER>\.nuget\packages\jetbrains.annotations\2023.3.0\lib\netstandard2.0\JetBrains.Annotations.dll
C:\Users\<USER>\.nuget\packages\jint\3.0.0-beta-1629\lib\netstandard2.0\Jint.dll
C:\Users\<USER>\.nuget\packages\mailkit\2.4.1\lib\netstandard2.0\MailKit.dll
C:\Users\<USER>\.nuget\packages\mediatr\8.0.0\lib\netstandard2.0\MediatR.dll
C:\Users\<USER>\.nuget\packages\mediatr.extensions.microsoft.dependencyinjection\8.0.0\lib\netstandard2.0\MediatR.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.authentication.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authentication.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.authentication.core\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authentication.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.authorization\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.authorization.policy\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authorization.Policy.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.cryptography.internal\3.1.1\lib\netstandard2.0\Microsoft.AspNetCore.Cryptography.Internal.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.dataprotection\3.1.1\lib\netcoreapp3.1\Microsoft.AspNetCore.DataProtection.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.dataprotection.abstractions\3.1.1\lib\netstandard2.0\Microsoft.AspNetCore.DataProtection.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.hosting.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.hosting.server.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.http\2.2.2\lib\netstandard2.0\Microsoft.AspNetCore.Http.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.http.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.http.extensions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.http.features\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.mvc.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.mvc.core\2.2.5\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.responsecaching.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.routing\2.2.0\lib\netcoreapp2.2\Microsoft.AspNetCore.Routing.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.routing.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Routing.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.webutilities\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll
C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\8.0.0\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\4.5.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\4.5.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.caching.abstractions\3.1.1\lib\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.caching.memory\3.1.1\lib\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.binder\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.commandline\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.environmentvariables\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.fileextensions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.json\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.usersecrets\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\8.0.0\lib\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencymodel\3.1.1\lib\netstandard2.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.composite\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Composite.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.embedded\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Embedded.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.physical\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.filesystemglobbing\8.0.0\lib\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.http\3.1.1\lib\netcoreapp3.1\Microsoft.Extensions.Http.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.localization\8.0.0\lib\net8.0\Microsoft.Extensions.Localization.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.localization.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Localization.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.configuration\3.1.1\lib\netcoreapp3.1\Microsoft.Extensions.Logging.Configuration.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.console\3.1.1\lib\netcoreapp3.1\Microsoft.Extensions.Logging.Console.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.debug\3.1.1\lib\netcoreapp3.1\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.objectpool\2.2.0\lib\netstandard2.0\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\lib\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.options.configurationextensions\8.0.0\lib\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\8.0.0\lib\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.net.http.headers\2.2.0\lib\netstandard2.0\Microsoft.Net.Http.Headers.dll
C:\Users\<USER>\.nuget\packages\microsoft.win32.registry\4.7.0\lib\netstandard2.0\Microsoft.Win32.Registry.dll
C:\Users\<USER>\.nuget\packages\microsoft.win32.systemevents\4.7.0\lib\netstandard2.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\.nuget\packages\mimekit\2.4.1\lib\netstandard2.0\MimeKit.dll
C:\Users\<USER>\.nuget\packages\ncrontab\3.3.1\lib\netstandard2.0\NCrontab.dll
C:\Users\<USER>\.nuget\packages\newtonsoft.json\13.0.1\lib\netstandard2.0\Newtonsoft.Json.dll
C:\Users\<USER>\.nuget\packages\nito.asyncex.context\5.1.2\lib\netstandard2.0\Nito.AsyncEx.Context.dll
C:\Users\<USER>\.nuget\packages\nito.asyncex.tasks\5.1.2\lib\netstandard2.0\Nito.AsyncEx.Tasks.dll
C:\Users\<USER>\.nuget\packages\nito.disposables\2.2.1\lib\netstandard2.1\Nito.Disposables.dll
C:\Users\<USER>\.nuget\packages\nodatime\3.0.0-beta01\lib\netstandard2.0\NodaTime.dll
C:\Users\<USER>\.nuget\packages\nodatime.serialization.jsonnet\2.2.0\lib\netstandard2.0\NodaTime.Serialization.JsonNet.dll
C:\Users\<USER>\.nuget\packages\nuget.common\6.2.2\lib\netstandard2.0\NuGet.Common.dll
C:\Users\<USER>\.nuget\packages\nuget.configuration\6.2.2\lib\netstandard2.0\NuGet.Configuration.dll
C:\Users\<USER>\.nuget\packages\nuget.frameworks\6.2.2\lib\netstandard2.0\NuGet.Frameworks.dll
C:\Users\<USER>\.nuget\packages\nuget.packaging\6.2.2\lib\net5.0\NuGet.Packaging.dll
C:\Users\<USER>\.nuget\packages\nuget.protocol\6.2.2\lib\net5.0\NuGet.Protocol.dll
C:\Users\<USER>\.nuget\packages\nuget.versioning\6.2.2\lib\netstandard2.0\NuGet.Versioning.dll
C:\Users\<USER>\.nuget\packages\nuglify\1.21.0\lib\net5.0\NUglify.dll
C:\Users\<USER>\.nuget\packages\portable.bouncycastle\1.8.5\lib\netstandard2.0\BouncyCastle.Crypto.dll
C:\Users\<USER>\.nuget\packages\scriban\5.10.0\lib\net7.0\Scriban.dll
C:\Users\<USER>\.nuget\packages\serilog\4.0.0\lib\net8.0\Serilog.dll
C:\Users\<USER>\.nuget\packages\serilog.extensions.logging\8.0.0\lib\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.console\6.0.0\lib\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.file\6.0.0\lib\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\.nuget\packages\system.appcontext\4.3.0\lib\netstandard1.6\System.AppContext.dll
C:\Users\<USER>\.nuget\packages\system.collections.concurrent\4.3.0\lib\netstandard1.3\System.Collections.Concurrent.dll
C:\Users\<USER>\.nuget\packages\system.collections.immutable\8.0.0\lib\net8.0\System.Collections.Immutable.dll
C:\Users\<USER>\.nuget\packages\system.commandline\2.0.0-beta1.20371.2\lib\netstandard2.0\System.CommandLine.dll
C:\Users\<USER>\.nuget\packages\system.componentmodel.annotations\4.7.0\lib\netstandard2.1\System.ComponentModel.Annotations.dll
C:\Users\<USER>\.nuget\packages\system.data.common\4.3.0\lib\netstandard1.2\System.Data.Common.dll
C:\Users\<USER>\.nuget\packages\system.diagnostics.diagnosticsource\8.0.0\lib\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\.nuget\packages\system.diagnostics.eventlog\6.0.0\lib\net6.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\.nuget\packages\system.drawing.common\4.7.0\lib\netstandard2.0\System.Drawing.Common.dll
C:\Users\<USER>\.nuget\packages\system.formats.asn1\5.0.0\lib\netstandard2.0\System.Formats.Asn1.dll
C:\Users\<USER>\.nuget\packages\system.io.compression.zipfile\4.3.0\lib\netstandard1.3\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\.nuget\packages\system.io.filesystem.primitives\4.3.0\lib\netstandard1.3\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\.nuget\packages\system.linq\4.3.0\lib\netstandard1.6\System.Linq.dll
C:\Users\<USER>\.nuget\packages\system.linq.dynamic.core\1.3.5\lib\net7.0\System.Linq.Dynamic.Core.dll
C:\Users\<USER>\.nuget\packages\system.linq.expressions\4.3.0\lib\netstandard1.6\System.Linq.Expressions.dll
C:\Users\<USER>\.nuget\packages\system.linq.queryable\4.3.0\lib\netstandard1.3\System.Linq.Queryable.dll
C:\Users\<USER>\.nuget\packages\system.objectmodel\4.3.0\lib\netstandard1.3\System.ObjectModel.dll
C:\Users\<USER>\.nuget\packages\system.reflection.emit\4.3.0\lib\netstandard1.3\System.Reflection.Emit.dll
C:\Users\<USER>\.nuget\packages\system.reflection.emit.ilgeneration\4.3.0\lib\netstandard1.3\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\.nuget\packages\system.reflection.emit.lightweight\4.3.0\lib\netstandard1.3\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\.nuget\packages\system.reflection.metadata\6.0.1\lib\net6.0\System.Reflection.Metadata.dll
C:\Users\<USER>\.nuget\packages\system.reflection.typeextensions\4.3.0\lib\netstandard1.5\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\.nuget\packages\system.runtime.compilerservices.unsafe\6.0.0\lib\net6.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\.nuget\packages\system.runtime.interopservices.runtimeinformation\4.3.0\lib\netstandard1.1\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\.nuget\packages\system.runtime.loader\4.3.0\lib\netstandard1.5\System.Runtime.Loader.dll
C:\Users\<USER>\.nuget\packages\system.runtime.numerics\4.3.0\lib\netstandard1.3\System.Runtime.Numerics.dll
C:\Users\<USER>\.nuget\packages\system.runtime.serialization.primitives\4.3.0\lib\netstandard1.3\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\.nuget\packages\system.security.accesscontrol\4.7.0\lib\netstandard2.0\System.Security.AccessControl.dll
C:\Users\<USER>\.nuget\packages\system.security.claims\4.3.0\lib\netstandard1.3\System.Security.Claims.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.cng\5.0.0\lib\netcoreapp3.0\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.openssl\4.3.0\lib\netstandard1.6\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.pkcs\5.0.0\lib\netcoreapp3.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.primitives\4.3.0\lib\netstandard1.3\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.protecteddata\4.4.0\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.xml\4.7.0\lib\netstandard2.0\System.Security.Cryptography.Xml.dll
C:\Users\<USER>\.nuget\packages\system.security.permissions\4.7.0\lib\netcoreapp3.0\System.Security.Permissions.dll
C:\Users\<USER>\.nuget\packages\system.security.principal\4.3.0\lib\netstandard1.0\System.Security.Principal.dll
C:\Users\<USER>\.nuget\packages\system.security.principal.windows\4.7.0\lib\netstandard2.0\System.Security.Principal.Windows.dll
C:\Users\<USER>\.nuget\packages\system.text.encoding.codepages\6.0.0\lib\net6.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\.nuget\packages\system.text.encodings.web\8.0.0\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\.nuget\packages\system.text.json\8.0.0\lib\net8.0\System.Text.Json.dll
C:\Users\<USER>\.nuget\packages\system.text.regularexpressions\4.3.0\lib\netstandard1.6\System.Text.RegularExpressions.dll
C:\Users\<USER>\.nuget\packages\system.threading\4.3.0\lib\netstandard1.3\System.Threading.dll
C:\Users\<USER>\.nuget\packages\system.threading.threadpool\4.3.0\lib\netstandard1.3\System.Threading.ThreadPool.dll
C:\Users\<USER>\.nuget\packages\system.windows.extensions\4.7.0\lib\netcoreapp3.0\System.Windows.Extensions.dll
C:\Users\<USER>\.nuget\packages\system.xml.readerwriter\4.3.0\lib\netstandard1.3\System.Xml.ReaderWriter.dll
C:\Users\<USER>\.nuget\packages\system.xml.xdocument\4.3.0\lib\netstandard1.3\System.Xml.XDocument.dll
C:\Users\<USER>\.nuget\packages\timezoneconverter\6.1.0\lib\net6.0\TimeZoneConverter.dll
C:\Users\<USER>\.nuget\packages\volo.abp.autofac\8.1.1\lib\net8.0\Volo.Abp.Autofac.dll
C:\Users\<USER>\.nuget\packages\volo.abp.castle.core\8.1.1\lib\net8.0\Volo.Abp.Castle.Core.dll
C:\Users\<USER>\.nuget\packages\volo.abp.core\8.1.1\lib\net8.0\Volo.Abp.Core.dll
C:\Users\<USER>\.nuget\packages\volo.abp.data\8.1.1\lib\net8.0\Volo.Abp.Data.dll
C:\Users\<USER>\.nuget\packages\volo.abp.eventbus.abstractions\8.1.1\lib\net8.0\Volo.Abp.EventBus.Abstractions.dll
C:\Users\<USER>\.nuget\packages\volo.abp.http\8.1.1\lib\net8.0\Volo.Abp.Http.dll
C:\Users\<USER>\.nuget\packages\volo.abp.http.abstractions\8.1.1\lib\net8.0\Volo.Abp.Http.Abstractions.dll
C:\Users\<USER>\.nuget\packages\volo.abp.json\8.1.1\lib\net8.0\Volo.Abp.Json.dll
C:\Users\<USER>\.nuget\packages\volo.abp.json.abstractions\8.1.1\lib\net8.0\Volo.Abp.Json.Abstractions.dll
C:\Users\<USER>\.nuget\packages\volo.abp.json.systemtextjson\8.1.1\lib\net8.0\Volo.Abp.Json.SystemTextJson.dll
C:\Users\<USER>\.nuget\packages\volo.abp.localization\8.1.1\lib\net8.0\Volo.Abp.Localization.dll
C:\Users\<USER>\.nuget\packages\volo.abp.localization.abstractions\8.1.1\lib\net8.0\Volo.Abp.Localization.Abstractions.dll
C:\Users\<USER>\.nuget\packages\volo.abp.minify\8.1.1\lib\net8.0\Volo.Abp.Minify.dll
C:\Users\<USER>\.nuget\packages\volo.abp.objectextending\8.1.1\lib\net8.0\Volo.Abp.ObjectExtending.dll
C:\Users\<USER>\.nuget\packages\volo.abp.security\8.1.1\lib\net8.0\Volo.Abp.Security.dll
C:\Users\<USER>\.nuget\packages\volo.abp.settings\8.1.1\lib\net8.0\Volo.Abp.Settings.dll
C:\Users\<USER>\.nuget\packages\volo.abp.threading\8.1.1\lib\net8.0\Volo.Abp.Threading.dll
C:\Users\<USER>\.nuget\packages\volo.abp.timing\8.1.1\lib\net8.0\Volo.Abp.Timing.dll
C:\Users\<USER>\.nuget\packages\volo.abp.uow\8.1.1\lib\net8.0\Volo.Abp.Uow.dll
C:\Users\<USER>\.nuget\packages\volo.abp.validation.abstractions\8.1.1\lib\net8.0\Volo.Abp.Validation.Abstractions.dll
C:\Users\<USER>\.nuget\packages\volo.abp.virtualfilesystem\8.1.1\lib\net8.0\Volo.Abp.VirtualFileSystem.dll
C:\Users\<USER>\.nuget\packages\yamldotnet\8.0.0\lib\netstandard2.1\YamlDotNet.dll
