﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Translate;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Translate
{
    public partial class CreateTranslationFile
    {
        [Inject]
        private IAbpCliTranslateAppService Service { get; set; }

        protected override async Task InternalExecuteAsync()
        {
            await Service.CreateTranslationFileAsync(Input);
        }
    }
}
