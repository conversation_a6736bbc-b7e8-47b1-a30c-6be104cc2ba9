﻿using System;
using System.ComponentModel.DataAnnotations;
using AbpTools.AbpHelper.Gui.CodeGeneration.Shared.Dtos;
using JetBrains.Annotations;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Localization.Dtos
{
    [Serializable]
    public class AbpHelperGenerateLocalizationItemsInput : AbpHelperGenerateInput
    {
        [Required]
        [NotNull]
        public virtual string Names { get; set; }

        public AbpHelperGenerateLocalizationItemsInput()
        {
        }

        public AbpHelperGenerateLocalizationItemsInput([NotNull] string directory, [CanBeNull] string projectName,
            [CanBeNull] string exclude, bool noOverwrite, [NotNull] string names) : base(directory, projectName,
            exclude, noOverwrite)
        {
            Names = names;
        }
    }
}