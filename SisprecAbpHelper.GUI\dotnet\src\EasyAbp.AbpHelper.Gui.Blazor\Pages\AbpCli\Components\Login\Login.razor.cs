﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Login;
using AbpTools.AbpHelper.Gui.AbpCli.Login.Dtos;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Login
{
    public partial class Login
    {
        [Inject]
        private IAbpCliLoginAppService Service { get; set; }

        protected AbpLoginInput Input { get; set; } = new();

        protected override async Task InternalExecuteAsync()
        {
            await Service.LoginAsync(Input);
        }
    }
}
