using System.Linq;
using AutoMapper;
using AbpTools.AbpHelper.Core.Commands.Ef.Migrations.Add;
using AbpTools.AbpHelper.Core.Commands.Ef.Migrations.Remove;
using AbpTools.AbpHelper.Core.Commands.Generate.Controller;
using AbpTools.AbpHelper.Core.Commands.Generate.Crud;
using AbpTools.AbpHelper.Core.Commands.Generate.Localization;
using AbpTools.AbpHelper.Core.Commands.Generate.Methods;
using AbpTools.AbpHelper.Core.Commands.Generate.Service;
using AbpTools.AbpHelper.Gui.CodeGeneration.AppService.Dtos;
using AbpTools.AbpHelper.Gui.CodeGeneration.Controller.Dtos;
using AbpTools.AbpHelper.Gui.CodeGeneration.Crud.Dtos;
using AbpTools.AbpHelper.Gui.CodeGeneration.Localization.Dtos;
using AbpTools.AbpHelper.Gui.CodeGeneration.Migration.Dtos;

namespace AbpTools.AbpHelper.Gui
{
    public class GuiApplicationAutoMapperProfile : Profile
    {
        public GuiApplicationAutoMapperProfile()
        {
            /* You can configure your AutoMapper mapping configuration here.
             * Alternatively, you can split your mapping configurations
             * into multiple profile classes for a better organization. */
            CreateMap<AbpHelperGenerateCrudInput, CrudCommandOption>()
                .ForMember(dest => dest.Exclude,
                    opt => opt.MapFrom(src => src.Exclude.SplitBySpace().ToArray()));
            
            CreateMap<AbpHelperGenerateAppServiceClassInput, ServiceCommandOption>()
                .ForMember(dest => dest.Exclude,
                    opt => opt.MapFrom(src => src.Exclude.SplitBySpace().ToArray()));
            
            CreateMap<AbpHelperGenerateAppServiceMethodsInput, MethodsCommandOption>()
                .ForMember(dest => dest.Exclude,
                    opt => opt.MapFrom(src => src.Exclude.SplitBySpace().ToArray()))
                .ForMember(dest => dest.MethodNames,
                    opt => opt.MapFrom(src => src.MethodNames.SplitBySpace().ToArray()));
            
            CreateMap<AbpHelperGenerateControllerInput, ControllerCommandOption>()
                .ForMember(dest => dest.Exclude,
                    opt => opt.MapFrom(src => src.Exclude.SplitBySpace().ToArray()));
            
            CreateMap<AbpHelperGenerateLocalizationItemsInput, LocalizationCommandOption>()
                .ForMember(dest => dest.Exclude,
                    opt => opt.MapFrom(src => src.Exclude.SplitBySpace().ToArray()))
                .ForMember(dest => dest.Names,
                    opt => opt.MapFrom(src => src.Names.SplitBySpace().ToArray()));

            CreateMap<AbpHelperGenerateMigrationAddInput, AddCommandOption>()
                .ForMember(dest => dest.EfOptions, opt => opt.MapFrom(src => src.EfOptions.SplitBySpace().ToArray()))
                .ForMember(dest => dest.Exclude,
                    opt => opt.MapFrom(src => src.Exclude.SplitBySpace().ToArray()));
            
            CreateMap<AbpHelperGenerateMigrationRemoveInput, RemoveCommandOption>()
                .ForMember(dest => dest.EfOptions, opt => opt.MapFrom(src => src.EfOptions.SplitBySpace().ToArray()))
                .ForMember(dest => dest.Exclude,
                    opt => opt.MapFrom(src => src.Exclude.SplitBySpace().ToArray()));
        }
    }
}
