﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Translate.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;
using Volo.Abp.Application.Services;

namespace AbpTools.AbpHelper.Gui.AbpCli.Translate
{
    public interface IAbpCliTranslateAppService : IApplicationService
    {
        Task<ServiceExecutionResult> CreateTranslationFileAsync(AbpCreateTranslationFileInput input);
        
        Task<ServiceExecutionResult> ApplyChangesAsync(AbpApplyChangesInput input);
    }
}