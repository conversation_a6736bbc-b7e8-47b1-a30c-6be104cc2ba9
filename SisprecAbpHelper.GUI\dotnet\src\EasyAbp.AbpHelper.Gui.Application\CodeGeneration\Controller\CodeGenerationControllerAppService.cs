using System.Threading.Tasks;
using AbpTools.AbpHelper.Core.Commands.Generate.Controller;
using AbpTools.AbpHelper.Gui.CodeGeneration.Controller.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Controller
{
    public class CodeGenerationControllerAppService : CodeGenerationAppService, ICodeGenerationControllerAppService
    {
        private readonly ControllerCommand _controllerCommand;

        public CodeGenerationControllerAppService(ControllerCommand controllerCommand)
        {
            _controllerCommand = controllerCommand;
        }
        
        public virtual async Task<ServiceExecutionResult> GenerateAsync(AbpHelperGenerateControllerInput input)
        {
            await _controllerCommand.RunCommand(ObjectMapper.Map<AbpHelperGenerateControllerInput, ControllerCommandOption>(input));

            return new ServiceExecutionResult(true);
        }
    }
}