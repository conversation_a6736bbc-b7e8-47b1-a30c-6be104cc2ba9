﻿using System.Threading.Tasks;
using AbpTools.AbpHelper.Gui.AbpCli.Translate;
using Microsoft.AspNetCore.Components;

namespace AbpTools.AbpHelper.Gui.Blazor.Pages.AbpCli.Components.Translate
{
    public partial class ApplyChanges
    {
        [Inject]
        private IAbpCliTranslateAppService Service { get; set; }

        protected override async Task InternalExecuteAsync()
        {
            await Service.ApplyChangesAsync(Input);
        }
    }
}
