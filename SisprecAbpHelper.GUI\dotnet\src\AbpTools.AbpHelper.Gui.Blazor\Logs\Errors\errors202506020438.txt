2025-06-02 04:38:52.326 -03:00 [ERR] Unhandled exception in circuit 'e4TlAHuLqxwirp2QlUhCDXCdbHOu9n1Zypdr5LviYeQ'.
System.InvalidOperationException: The current thread is not associated with the Dispatcher. Use InvokeAsync() to switch execution to the Dispatcher when triggering rendering or component state.
   at Microsoft.AspNetCore.Components.Dispatcher.AssertAccess()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at AbpTools.AbpHelper.Gui.Blazor.Pages.Solutions.Shared.SolutionManagementBase.StateHasChangedAsync() in D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\dotnet\src\AbpTools.AbpHelper.Gui.Blazor\Pages\Solutions\Shared\SolutionManagementBase.cs:line 134
   at AbpTools.AbpHelper.Gui.Blazor.Services.CurrentSolution.NotifyStateChanged() in D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\dotnet\src\AbpTools.AbpHelper.Gui.Blazor\Services\CurrentSolution.cs:line 24
   at AbpTools.AbpHelper.Gui.Blazor.Services.CurrentSolution.SetAsync(SolutionDto solutionDto) in D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\dotnet\src\AbpTools.AbpHelper.Gui.Blazor\Services\CurrentSolution.cs:line 18
   at AbpTools.AbpHelper.Gui.Blazor.Pages.Solutions.Shared.SolutionManagementBase.RefreshSolutions() in D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\dotnet\src\AbpTools.AbpHelper.Gui.Blazor\Pages\Solutions\Shared\SolutionManagementBase.cs:line 58
   at AbpTools.AbpHelper.Gui.Blazor.Pages.Solutions.Shared.SolutionManagementBase.OnInitializedAsync() in D:\Sisprec-AbpHelper\SisprecAbpHelper.GUI\dotnet\src\AbpTools.AbpHelper.Gui.Blazor\Pages\Solutions\Shared\SolutionManagementBase.cs:line 44
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
