using System.Threading.Tasks;
using AbpTools.AbpHelper.Core.Commands.Generate.Localization;
using AbpTools.AbpHelper.Gui.CodeGeneration.Localization;
using AbpTools.AbpHelper.Gui.CodeGeneration.Localization.Dtos;
using AbpTools.AbpHelper.Gui.Shared.Dtos;

namespace AbpTools.AbpHelper.Gui.CodeGeneration.Localization
{
    public class CodeGenerationLocalizationAppService : CodeGenerationAppService, ICodeGenerationLocalizationAppService
    {
        private readonly LocalizationCommand _localizationCommand;

        public CodeGenerationLocalizationAppService(LocalizationCommand localizationCommand)
        {
            _localizationCommand = localizationCommand;
        }

        public virtual async Task<ServiceExecutionResult> GenerateItemsAsync(AbpHelperGenerateLocalizationItemsInput input)
        {
            await _localizationCommand.RunCommand(ObjectMapper.Map<AbpHelperGenerateLocalizationItemsInput, LocalizationCommandOption>(input));

            return new ServiceExecutionResult(true);
        }
    }
}