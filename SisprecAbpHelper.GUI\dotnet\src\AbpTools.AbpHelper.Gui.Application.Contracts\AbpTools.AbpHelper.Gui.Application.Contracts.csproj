<Project Sdk="Microsoft.NET.Sdk">
    <Import Project="..\..\common.props" />
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>AbpTools.AbpHelper.Gui</RootNamespace>
        <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Volo.Abp.ObjectExtending"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Account.Application.Contracts"
                          Version="$(AbpVersion)" />
        <PackageReference Include="Volo.Abp.Identity.Application.Contracts"
                          Version="$(AbpVersion)" />
    </ItemGroup>
    <ItemGroup>
        <EmbeddedResource Include="Localization\AbpUi\*.json" />
        <Content Remove="Localization\AbpUi\*.json" />
        <EmbeddedResource Include="Localization\Gui\*.json" />
        <Content Remove="Localization\Gui\*.json" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded"
                          Version="9.0.0" />
    </ItemGroup>
</Project>